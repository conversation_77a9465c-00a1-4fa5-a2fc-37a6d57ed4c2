using Prism.Events;
using LabDemo.Domain.Entities;
using LabDemo.Domain.Enums;
using LabDemo.Service.Interfaces;
using LabDemo.Service.Events;

namespace LabDemo.Service.Implementations;

/// <summary>
/// 事件发布服务实现
///
/// 这个类展示了如何在服务层使用事件聚合器
/// 它将业务操作转换为事件，供UI层和其他组件订阅
/// </summary>
public class EventPublisher : IEventPublisher
{
    private readonly IEventAggregator _eventAggregator;

    public EventPublisher(IEventAggregator eventAggregator)
    {
        _eventAggregator = eventAggregator ?? throw new ArgumentNullException(nameof(eventAggregator));
    }

    public void PublishExperimentStatusChanged(string experimentId, string experimentName,
        ExperimentStatus oldStatus, ExperimentStatus newStatus,
        string? reason = null, string? userId = null)
    {
        var eventArgs = new ExperimentStatusChangedEventArgs
        {
            ExperimentId = experimentId,
            ExperimentName = experimentName,
            OldStatus = oldStatus,
            NewStatus = newStatus,
            Reason = reason,
            UserId = userId,
            Timestamp = DateTime.Now
        };

        _eventAggregator.GetEvent<ExperimentStatusChangedEvent>().Publish(eventArgs);

        var statusMessage = GetStatusChangeMessage(experimentName, oldStatus, newStatus);
        PublishInfoNotification(statusMessage);
    }

    public void PublishExperimentCreated(Experiment experiment, string? createdBy = null)
    {
        var eventArgs = new ExperimentCreatedEventArgs
        {
            Experiment = experiment,
            CreatedBy = createdBy,
            Timestamp = DateTime.Now
        };

        _eventAggregator.GetEvent<ExperimentCreatedEvent>().Publish(eventArgs);
        PublishSuccessNotification($"实验 '{experiment.Name}' 创建成功");
    }

    public void PublishExperimentDeleted(string experimentId, string experimentName,
        string? deletedBy = null, string? reason = null)
    {
        var eventArgs = new ExperimentDeletedEventArgs
        {
            ExperimentId = experimentId,
            ExperimentName = experimentName,
            DeletedBy = deletedBy,
            Reason = reason,
            Timestamp = DateTime.Now
        };

        _eventAggregator.GetEvent<ExperimentDeletedEvent>().Publish(eventArgs);
        PublishInfoNotification($"实验 '{experimentName}' 已删除");
    }

    public void PublishExperimentProgressUpdated(string experimentId, string currentStepName,
        int totalSteps, int completedSteps, TimeSpan? estimatedRemainingTime = null)
    {
        var progressPercentage = totalSteps > 0 ? (double)completedSteps / totalSteps * 100 : 0;

        var eventArgs = new ExperimentProgressUpdatedEventArgs
        {
            ExperimentId = experimentId,
            CurrentStepName = currentStepName,
            TotalSteps = totalSteps,
            CompletedSteps = completedSteps,
            ProgressPercentage = progressPercentage,
            EstimatedRemainingTime = estimatedRemainingTime,
            Timestamp = DateTime.Now
        };

        _eventAggregator.GetEvent<ExperimentProgressUpdatedEvent>().Publish(eventArgs);
    }

    public void PublishSystemNotification(string title, string message,
        Events.NotificationType type = Events.NotificationType.Information,
        bool autoClose = true, int autoCloseDelay = 5)
    {
        var eventArgs = new SystemNotificationEventArgs
        {
            Title = title,
            Message = message,
            Type = type,
            AutoClose = autoClose,
            AutoCloseDelay = autoCloseDelay,
            Timestamp = DateTime.Now
        };

        _eventAggregator.GetEvent<SystemNotificationEvent>().Publish(eventArgs);
    }

    public void PublishSuccessNotification(string message)
    {
        PublishSystemNotification("成功", message, Events.NotificationType.Success);
    }

    public void PublishErrorNotification(string message)
    {
        PublishSystemNotification("错误", message, Events.NotificationType.Error, autoClose: false);
    }

    public void PublishWarningNotification(string message)
    {
        PublishSystemNotification("警告", message, Events.NotificationType.Warning);
    }

    public void PublishInfoNotification(string message)
    {
        PublishSystemNotification("信息", message, Events.NotificationType.Information);
    }

    private string GetStatusChangeMessage(string experimentName, ExperimentStatus oldStatus, ExperimentStatus newStatus)
    {
        return newStatus switch
        {
            ExperimentStatus.Running => $"实验 '{experimentName}' 开始执行",
            ExperimentStatus.Paused => $"实验 '{experimentName}' 已暂停",
            ExperimentStatus.Completed => $"实验 '{experimentName}' 执行完成",
            ExperimentStatus.Failed => $"实验 '{experimentName}' 执行失败",
            ExperimentStatus.Cancelled => $"实验 '{experimentName}' 已取消",
            _ => $"实验 '{experimentName}' 状态从 {GetStatusDisplayName(oldStatus)} 变更为 {GetStatusDisplayName(newStatus)}"
        };
    }

    private string GetStatusDisplayName(ExperimentStatus status)
    {
        return status switch
        {
            ExperimentStatus.Created => "已创建",
            ExperimentStatus.Running => "运行中",
            ExperimentStatus.Paused => "已暂停",
            ExperimentStatus.Completed => "已完成",
            ExperimentStatus.Failed => "已失败",
            ExperimentStatus.Cancelled => "已取消",
            _ => status.ToString()
        };
    }
}

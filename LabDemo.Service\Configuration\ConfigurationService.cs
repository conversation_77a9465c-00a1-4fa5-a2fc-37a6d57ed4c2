using System.Text.Json;

namespace LabDemo.Service.Configuration;

/// <summary>
/// 配置服务实现
/// 
/// 这个类展示了企业级应用的配置管理最佳实践：
/// 1. JSON文件存储：使用JSON格式存储配置，易于阅读和编辑
/// 2. 内存缓存：将配置加载到内存中，提高访问性能
/// 3. 类型安全：提供强类型的配置访问方法
/// 4. 变更通知：支持配置变更事件通知
/// 5. 异步操作：支持异步的保存和加载操作
/// </summary>
public class ConfigurationService : IConfigurationService
{
    private readonly string _configFilePath;
    private readonly Dictionary<string, object> _configCache = new();
    private readonly object _lockObject = new object();

    /// <summary>
    /// 配置变更事件
    /// </summary>
    public event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;

    public ConfigurationService(string configFilePath = "appsettings.json")
    {
        _configFilePath = configFilePath;
        
        // 初始化时加载配置
        _ = LoadConfigurationAsync();
    }

    /// <summary>
    /// 获取字符串配置值
    /// </summary>
    public string GetString(string key, string defaultValue = "")
    {
        lock (_lockObject)
        {
            if (_configCache.TryGetValue(key, out var value))
            {
                return value?.ToString() ?? defaultValue;
            }
            return defaultValue;
        }
    }

    /// <summary>
    /// 获取整数配置值
    /// </summary>
    public int GetInt(string key, int defaultValue = 0)
    {
        var stringValue = GetString(key);
        if (int.TryParse(stringValue, out var result))
        {
            return result;
        }
        return defaultValue;
    }

    /// <summary>
    /// 获取布尔配置值
    /// </summary>
    public bool GetBool(string key, bool defaultValue = false)
    {
        var stringValue = GetString(key);
        if (bool.TryParse(stringValue, out var result))
        {
            return result;
        }
        return defaultValue;
    }

    /// <summary>
    /// 获取双精度浮点配置值
    /// </summary>
    public double GetDouble(string key, double defaultValue = 0.0)
    {
        var stringValue = GetString(key);
        if (double.TryParse(stringValue, out var result))
        {
            return result;
        }
        return defaultValue;
    }

    /// <summary>
    /// 获取强类型配置对象
    /// 
    /// 这个方法展示了如何使用反射和JSON序列化来实现强类型配置：
    /// 1. 从缓存中获取配置节
    /// 2. 序列化为JSON字符串
    /// 3. 反序列化为目标类型
    /// </summary>
    public T GetSection<T>(string sectionKey) where T : class, new()
    {
        lock (_lockObject)
        {
            if (_configCache.TryGetValue(sectionKey, out var sectionValue))
            {
                try
                {
                    // 如果已经是目标类型，直接返回
                    if (sectionValue is T directValue)
                    {
                        return directValue;
                    }

                    // 序列化为JSON再反序列化为目标类型
                    var json = JsonSerializer.Serialize(sectionValue);
                    var result = JsonSerializer.Deserialize<T>(json);
                    return result ?? new T();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"反序列化配置节 '{sectionKey}' 失败: {ex.Message}");
                    return new T();
                }
            }

            return new T();
        }
    }

    /// <summary>
    /// 设置配置值
    /// </summary>
    public void SetValue(string key, object value)
    {
        object? oldValue;
        
        lock (_lockObject)
        {
            _configCache.TryGetValue(key, out oldValue);
            _configCache[key] = value;
        }

        // 触发配置变更事件
        OnConfigurationChanged(new ConfigurationChangedEventArgs
        {
            Key = key,
            OldValue = oldValue,
            NewValue = value
        });
    }

    /// <summary>
    /// 保存配置到文件
    /// </summary>
    public async Task SaveAsync()
    {
        try
        {
            Dictionary<string, object> configToSave;
            
            lock (_lockObject)
            {
                configToSave = new Dictionary<string, object>(_configCache);
            }

            var json = JsonSerializer.Serialize(configToSave, new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            await File.WriteAllTextAsync(_configFilePath, json);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"保存配置文件失败: {ex.Message}", ex);
        }
    }

    /// <summary>
    /// 重新加载配置
    /// </summary>
    public async Task ReloadAsync()
    {
        await LoadConfigurationAsync();
    }

    /// <summary>
    /// 加载配置文件
    /// </summary>
    private async Task LoadConfigurationAsync()
    {
        try
        {
            if (!File.Exists(_configFilePath))
            {
                // 如果配置文件不存在，创建默认配置
                await CreateDefaultConfigurationAsync();
                return;
            }

            var json = await File.ReadAllTextAsync(_configFilePath);
            var config = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(json);

            if (config != null)
            {
                lock (_lockObject)
                {
                    _configCache.Clear();
                    
                    foreach (var kvp in config)
                    {
                        _configCache[kvp.Key] = ConvertJsonElement(kvp.Value);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"加载配置文件失败: {ex.Message}");
            
            // 如果加载失败，使用默认配置
            await CreateDefaultConfigurationAsync();
        }
    }

    /// <summary>
    /// 创建默认配置
    /// </summary>
    private async Task CreateDefaultConfigurationAsync()
    {
        var defaultConfig = new ApplicationConfiguration();
        
        lock (_lockObject)
        {
            _configCache.Clear();
            _configCache["Application"] = defaultConfig;
            _configCache["Database"] = defaultConfig.Database;
            _configCache["Logging"] = defaultConfig.Logging;
            _configCache["UI"] = defaultConfig.UI;
            _configCache["Experiment"] = defaultConfig.Experiment;
        }

        // 保存默认配置到文件
        await SaveAsync();
    }

    /// <summary>
    /// 转换JsonElement为对象
    /// 
    /// 这个方法处理JSON反序列化的类型转换：
    /// 1. 基本类型直接转换
    /// 2. 对象类型递归转换
    /// 3. 数组类型转换为列表
    /// </summary>
    private object ConvertJsonElement(JsonElement element)
    {
        return element.ValueKind switch
        {
            JsonValueKind.String => element.GetString() ?? "",
            JsonValueKind.Number => element.TryGetInt32(out var intValue) ? intValue : element.GetDouble(),
            JsonValueKind.True => true,
            JsonValueKind.False => false,
            JsonValueKind.Null => null!,
            JsonValueKind.Object => ConvertJsonObject(element),
            JsonValueKind.Array => ConvertJsonArray(element),
            _ => element.ToString()
        };
    }

    /// <summary>
    /// 转换JSON对象
    /// </summary>
    private Dictionary<string, object> ConvertJsonObject(JsonElement element)
    {
        var result = new Dictionary<string, object>();
        
        foreach (var property in element.EnumerateObject())
        {
            result[property.Name] = ConvertJsonElement(property.Value);
        }
        
        return result;
    }

    /// <summary>
    /// 转换JSON数组
    /// </summary>
    private List<object> ConvertJsonArray(JsonElement element)
    {
        var result = new List<object>();
        
        foreach (var item in element.EnumerateArray())
        {
            result.Add(ConvertJsonElement(item));
        }
        
        return result;
    }

    /// <summary>
    /// 触发配置变更事件
    /// </summary>
    protected virtual void OnConfigurationChanged(ConfigurationChangedEventArgs e)
    {
        ConfigurationChanged?.Invoke(this, e);
    }
}

/// <summary>
/// 配置服务扩展方法
/// 
/// 这些扩展方法提供了更便捷的配置访问方式
/// </summary>
public static class ConfigurationServiceExtensions
{
    /// <summary>
    /// 获取应用程序配置
    /// </summary>
    public static ApplicationConfiguration GetApplicationConfiguration(this IConfigurationService configService)
    {
        return configService.GetSection<ApplicationConfiguration>("Application");
    }

    /// <summary>
    /// 获取数据库配置
    /// </summary>
    public static DatabaseConfiguration GetDatabaseConfiguration(this IConfigurationService configService)
    {
        return configService.GetSection<DatabaseConfiguration>("Database");
    }

    /// <summary>
    /// 获取日志配置
    /// </summary>
    public static LoggingConfiguration GetLoggingConfiguration(this IConfigurationService configService)
    {
        return configService.GetSection<LoggingConfiguration>("Logging");
    }

    /// <summary>
    /// 获取UI配置
    /// </summary>
    public static UIConfiguration GetUIConfiguration(this IConfigurationService configService)
    {
        return configService.GetSection<UIConfiguration>("UI");
    }

    /// <summary>
    /// 获取实验配置
    /// </summary>
    public static ExperimentConfiguration GetExperimentConfiguration(this IConfigurationService configService)
    {
        return configService.GetSection<ExperimentConfiguration>("Experiment");
    }
}

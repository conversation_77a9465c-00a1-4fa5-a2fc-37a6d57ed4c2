using LabDemo.Domain.Entities;
using LabDemo.Domain.Enums;
using LabDemo.Service.QueryBuilder;
using System.Linq.Expressions;

namespace LabDemo.Service.Examples;

/// <summary>
/// 查询构建器使用示例
/// 
/// 这个类展示了表达式树查询构建器的各种使用方式
/// 从简单的单条件查询到复杂的组合查询
/// </summary>
public static class QueryBuilderExamples
{
    /// <summary>
    /// 示例1：简单的等于查询
    /// 查找特定状态的实验
    /// </summary>
    public static Expression<Func<Experiment, bool>> Example1_SimpleEqual()
    {
        // 使用查询构建器
        var query = ExperimentQueryBuilder.Create()
            .WithStatus(ExperimentStatus.Running)
            .Build();

        // 等价的手写Lambda表达式：
        // Expression<Func<Experiment, bool>> manualQuery = e => e.Status == ExperimentStatus.Running;

        return query!;
    }

    /// <summary>
    /// 示例2：字符串包含查询
    /// 按名称搜索实验
    /// </summary>
    public static Expression<Func<Experiment, bool>> Example2_StringContains(string searchText)
    {
        var query = ExperimentQueryBuilder.Create()
            .SearchByName(searchText)
            .Build();

        // 等价的手写Lambda表达式：
        // Expression<Func<Experiment, bool>> manualQuery = e => e.Name.Contains(searchText);

        return query!;
    }

    /// <summary>
    /// 示例3：范围查询
    /// 查找指定优先级范围的实验
    /// </summary>
    public static Expression<Func<Experiment, bool>> Example3_RangeQuery(int minPriority, int maxPriority)
    {
        var query = ExperimentQueryBuilder.Create()
            .WithPriorityBetween(minPriority, maxPriority)
            .Build();

        // 等价的手写Lambda表达式：
        // Expression<Func<Experiment, bool>> manualQuery = e => e.Priority >= minPriority && e.Priority <= maxPriority;

        return query!;
    }

    /// <summary>
    /// 示例4：日期范围查询
    /// 查找指定时间段创建的实验
    /// </summary>
    public static Expression<Func<Experiment, bool>> Example4_DateRange(DateTime startDate, DateTime endDate)
    {
        var query = ExperimentQueryBuilder.Create()
            .CreatedBetween(startDate, endDate)
            .Build();

        return query!;
    }

    /// <summary>
    /// 示例5：In查询
    /// 查找指定类型列表中的实验
    /// </summary>
    public static Expression<Func<Experiment, bool>> Example5_InQuery(List<ExperimentType> types)
    {
        var query = ExpressionQueryBuilder<Experiment>.Create()
            .In(e => e.Type, types)
            .Build();

        return query!;
    }

    /// <summary>
    /// 示例6：复合AND查询
    /// 查找同时满足多个条件的实验
    /// </summary>
    public static Expression<Func<Experiment, bool>> Example6_MultipleAndConditions(
        string searchText, 
        ExperimentStatus status, 
        int minPriority)
    {
        var query = ExperimentQueryBuilder.Create()
            .SearchByName(searchText)
            .WithStatus(status)
            .WithPriorityBetween(minPriority, 5)
            .Build();

        // 等价的手写Lambda表达式：
        // Expression<Func<Experiment, bool>> manualQuery = e => 
        //     e.Name.Contains(searchText) && 
        //     e.Status == status && 
        //     e.Priority >= minPriority && 
        //     e.Priority <= 5;

        return query!;
    }

    /// <summary>
    /// 示例7：OR查询
    /// 查找满足任一条件的实验
    /// </summary>
    public static Expression<Func<Experiment, bool>> Example7_OrConditions()
    {
        var query = ExperimentQueryBuilder.Create()
            .Or(
                // 条件1：高优先级的运行中实验
                b => b.WithStatus(ExperimentStatus.Running)
                      .WithPriorityBetween(4, 5),
                
                // 条件2：最近失败的实验
                b => b.WithStatus(ExperimentStatus.Failed)
                      .RecentlyCreated(3)
            )
            .Build();

        return query!;
    }

    /// <summary>
    /// 示例8：复杂的组合查询
    /// 业务场景：查找需要关注的实验
    /// </summary>
    public static Expression<Func<Experiment, bool>> Example8_ComplexQuery(string currentUser)
    {
        var query = ExperimentQueryBuilder.Create()
            // 基础条件：我创建的实验
            .CreatedBy(currentUser)
            .Or(
                // 情况1：长时间运行的高优先级实验
                b => b.LongRunning(2)
                      .HighPriority(),
                
                // 情况2：最近失败的实验
                b => b.WithStatus(ExperimentStatus.Failed)
                      .RecentlyCreated(1),
                
                // 情况3：今天创建的重要实验
                b => b.CreatedBetween(DateTime.Today, DateTime.Today.AddDays(1))
                      .WithPriorityBetween(3, 5)
            )
            .Build();

        return query!;
    }

    /// <summary>
    /// 示例9：动态查询构建
    /// 根据用户输入动态构建查询条件
    /// </summary>
    public static Expression<Func<Experiment, bool>>? Example9_DynamicQuery(
        string? searchText = null,
        ExperimentType? type = null,
        ExperimentStatus? status = null,
        DateTime? startDate = null,
        DateTime? endDate = null,
        int? minPriority = null,
        int? maxPriority = null,
        bool onlyMyExperiments = false,
        string? currentUser = null)
    {
        var builder = ExperimentQueryBuilder.Create();

        // 动态添加条件
        if (!string.IsNullOrWhiteSpace(searchText))
        {
            builder.Or(
                b => b.SearchByName(searchText),
                b => b.SearchByDescription(searchText)
            );
        }

        if (type.HasValue)
        {
            builder.OfType(type.Value);
        }

        if (status.HasValue)
        {
            builder.WithStatus(status.Value);
        }

        if (startDate.HasValue && endDate.HasValue)
        {
            builder.CreatedBetween(startDate.Value, endDate.Value);
        }

        if (minPriority.HasValue && maxPriority.HasValue)
        {
            builder.WithPriorityBetween(minPriority.Value, maxPriority.Value);
        }

        if (onlyMyExperiments && !string.IsNullOrWhiteSpace(currentUser))
        {
            builder.CreatedBy(currentUser);
        }

        return builder.Build();
    }

    /// <summary>
    /// 示例10：性能优化查询
    /// 针对大数据量的优化查询
    /// </summary>
    public static Expression<Func<Experiment, bool>> Example10_OptimizedQuery()
    {
        // 优化策略：
        // 1. 最具选择性的条件放在前面
        // 2. 避免复杂的字符串操作
        // 3. 使用索引友好的条件

        var query = ExpressionQueryBuilder<Experiment>.Create()
            // 首先按状态筛选（假设有索引）
            .Where(e => e.Status, ExperimentStatus.Running)
            // 然后按创建时间筛选（假设有索引）
            .DateRange(e => e.CreatedAt, DateTime.Today.AddDays(-7), DateTime.Today.AddDays(1))
            // 最后按优先级筛选
            .Between(e => e.Priority, 3, 5)
            .Build();

        return query!;
    }

    /// <summary>
    /// 示例11：使用编译后的委托进行内存查询
    /// 展示如何将表达式树编译为委托用于内存集合查询
    /// </summary>
    public static List<Experiment> Example11_InMemoryQuery(
        List<Experiment> experiments, 
        string searchText)
    {
        // 构建查询表达式
        var queryExpression = ExperimentQueryBuilder.Create()
            .SearchByName(searchText)
            .WithStatus(ExperimentStatus.Running)
            .Build();

        if (queryExpression == null)
            return experiments;

        // 编译为委托
        var compiledQuery = queryExpression.Compile();

        // 在内存集合中执行查询
        return experiments.Where(compiledQuery).ToList();
    }

    /// <summary>
    /// 示例12：查询表达式的序列化和反序列化
    /// 展示如何保存和恢复查询条件
    /// </summary>
    public static string Example12_SerializeQuery()
    {
        var query = ExperimentQueryBuilder.Create()
            .WithStatus(ExperimentStatus.Running)
            .HighPriority()
            .Build();

        // 在实际应用中，可以将表达式树序列化为JSON或XML
        // 这里只是示例，实际的序列化需要专门的库
        return query?.ToString() ?? "";
    }

    /// <summary>
    /// 示例13：查询统计信息
    /// 使用表达式树进行数据统计
    /// </summary>
    public static Dictionary<string, object> Example13_QueryStatistics(List<Experiment> experiments)
    {
        var stats = new Dictionary<string, object>();

        // 按状态统计
        var runningQuery = ExperimentQueryBuilder.Create()
            .WithStatus(ExperimentStatus.Running)
            .Compile();

        var completedQuery = ExperimentQueryBuilder.Create()
            .WithStatus(ExperimentStatus.Completed)
            .Compile();

        var failedQuery = ExperimentQueryBuilder.Create()
            .WithStatus(ExperimentStatus.Failed)
            .Compile();

        if (runningQuery != null)
            stats["RunningCount"] = experiments.Count(runningQuery);
        
        if (completedQuery != null)
            stats["CompletedCount"] = experiments.Count(completedQuery);
        
        if (failedQuery != null)
            stats["FailedCount"] = experiments.Count(failedQuery);

        // 高优先级实验统计
        var highPriorityQuery = ExperimentQueryBuilder.Create()
            .HighPriority()
            .Compile();

        if (highPriorityQuery != null)
            stats["HighPriorityCount"] = experiments.Count(highPriorityQuery);

        // 最近创建的实验统计
        var recentQuery = ExperimentQueryBuilder.Create()
            .RecentlyCreated(7)
            .Compile();

        if (recentQuery != null)
            stats["RecentCount"] = experiments.Count(recentQuery);

        return stats;
    }
}

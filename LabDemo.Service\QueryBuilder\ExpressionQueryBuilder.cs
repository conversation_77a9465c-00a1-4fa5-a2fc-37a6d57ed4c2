using System.Linq.Expressions;
using System.Reflection;
using LabDemo.Domain.Entities;
using LabDemo.Domain.Enums;
using Expression = System.Linq.Expressions.Expression;

namespace LabDemo.Service.QueryBuilder;

/// <summary>
/// 表达式查询构建器
/// 
/// 什么是表达式树？
/// 表达式树是将代码表示为数据的一种方式。它将Lambda表达式转换为可以在运行时
/// 分析和操作的树形结构。
/// 
/// 为什么使用表达式树？
/// 1. 类型安全：编译时检查，避免字符串拼接SQL的错误
/// 2. 智能提示：IDE可以提供完整的智能提示和重构支持
/// 3. 可转换：可以转换为SQL、MongoDB查询等不同的查询语言
/// 4. 可组合：可以动态组合复杂的查询条件
/// 5. 性能优化：可以在运行时优化查询表达式
/// 
/// 表达式树的应用场景：
/// - ORM框架（如Entity Framework）
/// - 动态查询构建
/// - 规则引擎
/// - 数据验证
/// - 序列化/反序列化
/// </summary>
public class ExpressionQueryBuilder<T> where T : class
{
    private Expression<Func<T, bool>>? _currentExpression;

    /// <summary>
    /// 开始构建查询
    /// </summary>
    /// <returns>查询构建器实例</returns>
    public static ExpressionQueryBuilder<T> Create()
    {
        return new ExpressionQueryBuilder<T>();
    }

    /// <summary>
    /// 添加等于条件
    /// 
    /// 这个方法展示了如何动态构建表达式树：
    /// 1. 创建参数表达式
    /// 2. 创建属性访问表达式
    /// 3. 创建常量表达式
    /// 4. 创建比较表达式
    /// 5. 创建Lambda表达式
    /// </summary>
    /// <typeparam name="TProperty">属性类型</typeparam>
    /// <param name="propertySelector">属性选择器</param>
    /// <param name="value">比较值</param>
    /// <returns>查询构建器实例</returns>
    public ExpressionQueryBuilder<T> Where<TProperty>(
        Expression<Func<T, TProperty>> propertySelector, 
        TProperty value)
    {
        // 获取属性访问表达式
        var propertyExpression = propertySelector.Body;
        var parameter = propertySelector.Parameters[0];

        // 创建常量表达式
        var constantExpression = Expression.Constant(value, typeof(TProperty));

        // 创建等于比较表达式
        var equalExpression = Expression.Equal(propertyExpression, constantExpression);

        // 创建Lambda表达式
        var lambdaExpression = Expression.Lambda<Func<T, bool>>(equalExpression, parameter);

        // 组合到当前表达式
        _currentExpression = CombineWithAnd(_currentExpression, lambdaExpression);

        return this;
    }

    /// <summary>
    /// 添加包含条件（用于字符串）
    /// </summary>
    /// <param name="propertySelector">字符串属性选择器</param>
    /// <param name="value">包含的值</param>
    /// <returns>查询构建器实例</returns>
    public ExpressionQueryBuilder<T> Contains(
        Expression<Func<T, string>> propertySelector, 
        string value)
    {
        if (string.IsNullOrEmpty(value))
            return this;

        var propertyExpression = propertySelector.Body;
        var parameter = propertySelector.Parameters[0];

        // 获取String.Contains方法
        var containsMethod = typeof(string).GetMethod("Contains", new[] { typeof(string) })!;

        // 创建方法调用表达式
        var constantExpression = Expression.Constant(value);
        var containsExpression = Expression.Call(propertyExpression, containsMethod, constantExpression);

        // 添加null检查
        var nullCheckExpression = Expression.NotEqual(propertyExpression, Expression.Constant(null));
        var combinedExpression = Expression.AndAlso(nullCheckExpression, containsExpression);

        var lambdaExpression = Expression.Lambda<Func<T, bool>>(combinedExpression, parameter);

        _currentExpression = CombineWithAnd(_currentExpression, lambdaExpression);

        return this;
    }

    /// <summary>
    /// 添加范围条件
    /// </summary>
    /// <typeparam name="TProperty">属性类型</typeparam>
    /// <param name="propertySelector">属性选择器</param>
    /// <param name="minValue">最小值</param>
    /// <param name="maxValue">最大值</param>
    /// <returns>查询构建器实例</returns>
    public ExpressionQueryBuilder<T> Between<TProperty>(
        Expression<Func<T, TProperty>> propertySelector,
        TProperty minValue,
        TProperty maxValue) where TProperty : IComparable<TProperty>
    {
        var propertyExpression = propertySelector.Body;
        var parameter = propertySelector.Parameters[0];

        // 创建 >= minValue 表达式
        var minConstant = Expression.Constant(minValue);
        var greaterThanOrEqual = Expression.GreaterThanOrEqual(propertyExpression, minConstant);

        // 创建 <= maxValue 表达式
        var maxConstant = Expression.Constant(maxValue);
        var lessThanOrEqual = Expression.LessThanOrEqual(propertyExpression, maxConstant);

        // 组合条件
        var betweenExpression = Expression.AndAlso(greaterThanOrEqual, lessThanOrEqual);
        var lambdaExpression = Expression.Lambda<Func<T, bool>>(betweenExpression, parameter);

        _currentExpression = CombineWithAnd(_currentExpression, lambdaExpression);

        return this;
    }

    /// <summary>
    /// 添加In条件（值在列表中）
    /// </summary>
    /// <typeparam name="TProperty">属性类型</typeparam>
    /// <param name="propertySelector">属性选择器</param>
    /// <param name="values">值列表</param>
    /// <returns>查询构建器实例</returns>
    public ExpressionQueryBuilder<T> In<TProperty>(
        Expression<Func<T, TProperty>> propertySelector,
        IEnumerable<TProperty> values)
    {
        var valueList = values.ToList();
        if (!valueList.Any())
            return this;

        var propertyExpression = propertySelector.Body;
        var parameter = propertySelector.Parameters[0];

        // 创建值列表常量
        var valuesConstant = Expression.Constant(valueList);

        // 获取Contains方法
        var containsMethod = typeof(List<TProperty>).GetMethod("Contains", new[] { typeof(TProperty) })!;

        // 创建方法调用表达式
        var containsExpression = Expression.Call(valuesConstant, containsMethod, propertyExpression);
        var lambdaExpression = Expression.Lambda<Func<T, bool>>(containsExpression, parameter);

        _currentExpression = CombineWithAnd(_currentExpression, lambdaExpression);

        return this;
    }

    /// <summary>
    /// 添加日期范围条件
    /// </summary>
    /// <param name="propertySelector">日期属性选择器</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>查询构建器实例</returns>
    public ExpressionQueryBuilder<T> DateRange(
        Expression<Func<T, DateTime>> propertySelector,
        DateTime startDate,
        DateTime endDate)
    {
        return Between(propertySelector, startDate, endDate);
    }

    /// <summary>
    /// 添加可空日期范围条件
    /// </summary>
    /// <param name="propertySelector">可空日期属性选择器</param>
    /// <param name="startDate">开始日期</param>
    /// <param name="endDate">结束日期</param>
    /// <returns>查询构建器实例</returns>
    public ExpressionQueryBuilder<T> DateRange(
        Expression<Func<T, DateTime?>> propertySelector,
        DateTime startDate,
        DateTime endDate)
    {
        var propertyExpression = propertySelector.Body;
        var parameter = propertySelector.Parameters[0];

        // 检查不为null
        var notNullExpression = Expression.NotEqual(propertyExpression, Expression.Constant(null));

        // 获取Value属性
        var valueProperty = typeof(DateTime?).GetProperty("Value")!;
        var valueExpression = Expression.Property(propertyExpression, valueProperty);

        // 创建范围条件
        var startConstant = Expression.Constant(startDate);
        var endConstant = Expression.Constant(endDate);
        var greaterThanOrEqual = Expression.GreaterThanOrEqual(valueExpression, startConstant);
        var lessThanOrEqual = Expression.LessThanOrEqual(valueExpression, endConstant);

        // 组合所有条件
        var rangeExpression = Expression.AndAlso(greaterThanOrEqual, lessThanOrEqual);
        var finalExpression = Expression.AndAlso(notNullExpression, rangeExpression);

        var lambdaExpression = Expression.Lambda<Func<T, bool>>(finalExpression, parameter);

        _currentExpression = CombineWithAnd(_currentExpression, lambdaExpression);

        return this;
    }

    /// <summary>
    /// 添加OR条件组
    /// 
    /// 这个方法展示了如何构建复杂的逻辑表达式
    /// </summary>
    /// <param name="orConditions">OR条件构建器</param>
    /// <returns>查询构建器实例</returns>
    public ExpressionQueryBuilder<T> Or(params Func<ExpressionQueryBuilder<T>, ExpressionQueryBuilder<T>>[] orConditions)
    {
        if (!orConditions.Any())
            return this;

        Expression<Func<T, bool>>? orExpression = null;

        foreach (var conditionBuilder in orConditions)
        {
            var builder = new ExpressionQueryBuilder<T>();
            conditionBuilder(builder);

            if (builder._currentExpression != null)
            {
                orExpression = CombineWithOr(orExpression, builder._currentExpression);
            }
        }

        if (orExpression != null)
        {
            _currentExpression = CombineWithAnd(_currentExpression, orExpression);
        }

        return this;
    }

    /// <summary>
    /// 构建最终的表达式
    /// </summary>
    /// <returns>Lambda表达式</returns>
    public Expression<Func<T, bool>>? Build()
    {
        return _currentExpression;
    }

    /// <summary>
    /// 编译表达式为委托
    /// </summary>
    /// <returns>编译后的委托</returns>
    public Func<T, bool>? Compile()
    {
        return _currentExpression?.Compile();
    }

    /// <summary>
    /// 使用AND组合两个表达式
    /// </summary>
    private Expression<Func<T, bool>> CombineWithAnd(
        Expression<Func<T, bool>>? left,
        Expression<Func<T, bool>> right)
    {
        if (left == null)
            return right;

        return CombineExpressions(left, right, Expression.AndAlso);
    }

    /// <summary>
    /// 使用OR组合两个表达式
    /// </summary>
    private Expression<Func<T, bool>> CombineWithOr(
        Expression<Func<T, bool>>? left,
        Expression<Func<T, bool>> right)
    {
        if (left == null)
            return right;

        return CombineExpressions(left, right, Expression.OrElse);
    }

    /// <summary>
    /// 组合两个表达式
    /// 
    /// 这个方法展示了表达式树的核心操作：
    /// 1. 参数统一
    /// 2. 表达式体组合
    /// 3. Lambda表达式重建
    /// </summary>
    private Expression<Func<T, bool>> CombineExpressions(
        Expression<Func<T, bool>> left,
        Expression<Func<T, bool>> right,
        Func<Expression, Expression, BinaryExpression> combiner)
    {
        // 使用左表达式的参数
        var parameter = left.Parameters[0];

        // 替换右表达式的参数
        var rightBody = new ParameterReplacer(right.Parameters[0], parameter).Visit(right.Body);

        // 组合表达式体
        var combinedBody = combiner(left.Body, rightBody);

        // 创建新的Lambda表达式
        return Expression.Lambda<Func<T, bool>>(combinedBody, parameter);
    }
}

/// <summary>
/// 参数替换访问器
/// 
/// 这个类用于在表达式树中替换参数引用
/// 当组合多个表达式时，需要确保它们使用相同的参数
/// </summary>
public class ParameterReplacer : ExpressionVisitor
{
    private readonly ParameterExpression _oldParameter;
    private readonly ParameterExpression _newParameter;

    public ParameterReplacer(ParameterExpression oldParameter, ParameterExpression newParameter)
    {
        _oldParameter = oldParameter;
        _newParameter = newParameter;
    }

    protected override Expression VisitParameter(ParameterExpression node)
    {
        return node == _oldParameter ? _newParameter : base.VisitParameter(node);
    }
}

/// <summary>
/// 实验查询构建器 - 专门用于实验实体的查询构建
/// 
/// 这个类展示了如何为特定实体创建专门的查询构建器
/// 提供业务相关的查询方法
/// </summary>
public static class ExperimentQueryBuilder
{
    /// <summary>
    /// 创建实验查询构建器
    /// </summary>
    /// <returns>查询构建器实例</returns>
    public static ExpressionQueryBuilder<Experiment> Create()
    {
        return ExpressionQueryBuilder<Experiment>.Create();
    }

    /// <summary>
    /// 按名称搜索
    /// </summary>
    public static ExpressionQueryBuilder<Experiment> SearchByName(
        this ExpressionQueryBuilder<Experiment> builder, 
        string searchText)
    {
        if (!string.IsNullOrWhiteSpace(searchText))
        {
            builder.Contains(e => e.Name, searchText);
        }
        return builder;
    }

    /// <summary>
    /// 按描述搜索
    /// </summary>
    public static ExpressionQueryBuilder<Experiment> SearchByDescription(
        this ExpressionQueryBuilder<Experiment> builder, 
        string searchText)
    {
        if (!string.IsNullOrWhiteSpace(searchText))
        {
            builder.Contains(e => e.Description ?? "", searchText);
        }
        return builder;
    }

    /// <summary>
    /// 按类型筛选
    /// </summary>
    public static ExpressionQueryBuilder<Experiment> OfType(
        this ExpressionQueryBuilder<Experiment> builder, 
        ExperimentType type)
    {
        return builder.Where(e => e.Type, type);
    }

    /// <summary>
    /// 按状态筛选
    /// </summary>
    public static ExpressionQueryBuilder<Experiment> WithStatus(
        this ExpressionQueryBuilder<Experiment> builder, 
        ExperimentStatus status)
    {
        return builder.Where(e => e.Status, status);
    }

    /// <summary>
    /// 按创建者筛选
    /// </summary>
    public static ExpressionQueryBuilder<Experiment> CreatedBy(
        this ExpressionQueryBuilder<Experiment> builder, 
        string createdBy)
    {
        if (!string.IsNullOrWhiteSpace(createdBy))
        {
            builder.Where(e => e.CreatedBy ?? "", createdBy);
        }
        return builder;
    }

    /// <summary>
    /// 按创建时间范围筛选
    /// </summary>
    public static ExpressionQueryBuilder<Experiment> CreatedBetween(
        this ExpressionQueryBuilder<Experiment> builder, 
        DateTime startDate, 
        DateTime endDate)
    {
        return builder.DateRange(e => e.CreatedAt, startDate, endDate);
    }

    /// <summary>
    /// 按优先级范围筛选
    /// </summary>
    public static ExpressionQueryBuilder<Experiment> WithPriorityBetween(
        this ExpressionQueryBuilder<Experiment> builder, 
        int minPriority, 
        int maxPriority)
    {
        return builder.Between(e => e.Priority, minPriority, maxPriority);
    }

    /// <summary>
    /// 高优先级实验
    /// </summary>
    public static ExpressionQueryBuilder<Experiment> HighPriority(
        this ExpressionQueryBuilder<Experiment> builder)
    {
        return builder.Where(e => e.Priority, 4).Or(
            b => b.Where(e => e.Priority, 5)
        );
    }

    /// <summary>
    /// 最近创建的实验
    /// </summary>
    public static ExpressionQueryBuilder<Experiment> RecentlyCreated(
        this ExpressionQueryBuilder<Experiment> builder,
        int days = 7)
    {
        var cutoffDate = DateTime.Now.AddDays(-days);
        // 使用Between方法来实现日期范围查询
        return builder.DateRange(e => e.CreatedAt, cutoffDate, DateTime.Now);
    }

    /// <summary>
    /// 长时间运行的实验
    /// </summary>
    public static ExpressionQueryBuilder<Experiment> LongRunning(
        this ExpressionQueryBuilder<Experiment> builder,
        int hours = 2)
    {
        var cutoffTime = DateTime.Now.AddHours(-hours);
        return builder.WithStatus(ExperimentStatus.Running)
                     .DateRange(e => e.StartTime ?? DateTime.MinValue, DateTime.MinValue, cutoffTime);
    }
}

using LabDemo.Domain.Entities;
using LabDemo.Domain.Enums;
using LabDemo.Service.Interfaces;

namespace LabDemo.ExperimentModule.Services;

/// <summary>
/// 实验验证服务实现
/// 
/// 这个类展示了复杂业务验证逻辑的实现：
/// 1. 规则引擎：基于规则的验证框架
/// 2. 可配置验证：支持动态配置验证规则
/// 3. 多级验证：支持不同级别的验证消息
/// 4. 异步验证：支持需要数据库查询的验证
/// </summary>
public class ExperimentValidationService : IExperimentValidationService
{
    private readonly IExperimentService _experimentService;
    private readonly List<ValidationRule> _validationRules = new();

    public ExperimentValidationService(IExperimentService experimentService)
    {
        _experimentService = experimentService;
    }

    /// <summary>
    /// 加载验证规则
    /// </summary>
    public void LoadValidationRules()
    {
        // 初始化验证规则
        InitializeValidationRules();
        
        Console.WriteLine($"加载了 {_validationRules.Count} 个验证规则");
    }

    /// <summary>
    /// 验证实验数据
    /// </summary>
    public async Task<ValidationResult> ValidateExperimentAsync(Experiment experiment)
    {
        var result = new ValidationResult { IsValid = true };

        // 基础验证
        ValidateExperimentBasicInfo(experiment, result);

        // 业务规则验证
        await ValidateExperimentBusinessRulesAsync(experiment, result);

        return result;
    }

    /// <summary>
    /// 验证实验步骤
    /// </summary>
    public async Task<ValidationResult> ValidateExperimentStepAsync(ExperimentStep step)
    {
        var result = new ValidationResult { IsValid = true };

        // 验证步骤基本信息
        if (string.IsNullOrWhiteSpace(step.Name))
        {
            result.IsValid = false;
            result.ErrorMessages.Add("步骤名称不能为空");
        }

        if (step.EstimatedDurationMinutes <= 0)
        {
            result.IsValid = false;
            result.ErrorMessages.Add("预计执行时间必须大于0");
        }

        // 验证步骤参数
        if (!string.IsNullOrEmpty(step.Parameters))
        {
            try
            {
                var parameters = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(step.Parameters);
                // 这里可以添加参数验证逻辑
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.ErrorMessages.Add($"步骤参数格式无效: {ex.Message}");
            }
        }

        await Task.CompletedTask; // 模拟异步操作
        return result;
    }

    /// <summary>
    /// 验证实验是否可以开始
    /// </summary>
    public async Task<ValidationResult> ValidateExperimentCanStartAsync(string experimentId)
    {
        var result = new ValidationResult { IsValid = true };

        var experiment = await _experimentService.GetExperimentByIdAsync(experimentId);
        if (experiment == null)
        {
            result.IsValid = false;
            result.ErrorMessages.Add("实验不存在");
            return result;
        }

        if (experiment.Status != ExperimentStatus.Created)
        {
            result.IsValid = false;
            result.ErrorMessages.Add($"只有状态为'已创建'的实验才能开始，当前状态：{experiment.Status}");
        }

        // 验证是否有步骤
        if (!experiment.Steps.Any())
        {
            result.IsValid = false;
            result.ErrorMessages.Add("实验必须包含至少一个步骤");
        }

        // 验证所有步骤都是有效的
        foreach (var step in experiment.Steps)
        {
            var stepValidation = await ValidateExperimentStepAsync(step);
            if (!stepValidation.IsValid)
            {
                result.IsValid = false;
                result.ErrorMessages.AddRange(stepValidation.ErrorMessages.Select(msg => $"步骤 '{step.Name}': {msg}"));
            }
        }

        return result;
    }

    /// <summary>
    /// 验证实验是否可以暂停
    /// </summary>
    public async Task<ValidationResult> ValidateExperimentCanPauseAsync(string experimentId)
    {
        var result = new ValidationResult { IsValid = true };

        var experiment = await _experimentService.GetExperimentByIdAsync(experimentId);
        if (experiment == null)
        {
            result.IsValid = false;
            result.ErrorMessages.Add("实验不存在");
            return result;
        }

        if (experiment.Status != ExperimentStatus.Running)
        {
            result.IsValid = false;
            result.ErrorMessages.Add($"只有状态为'运行中'的实验才能暂停，当前状态：{experiment.Status}");
        }

        return result;
    }

    /// <summary>
    /// 验证实验是否可以完成
    /// </summary>
    public async Task<ValidationResult> ValidateExperimentCanCompleteAsync(string experimentId)
    {
        var result = new ValidationResult { IsValid = true };

        var experiment = await _experimentService.GetExperimentByIdAsync(experimentId);
        if (experiment == null)
        {
            result.IsValid = false;
            result.ErrorMessages.Add("实验不存在");
            return result;
        }

        if (experiment.Status != ExperimentStatus.Running)
        {
            result.IsValid = false;
            result.ErrorMessages.Add($"只有状态为'运行中'的实验才能完成，当前状态：{experiment.Status}");
        }

        // 验证所有步骤都已完成
        var incompleteSteps = experiment.Steps.Where(s => s.Status != StepStatus.Completed).ToList();
        if (incompleteSteps.Any())
        {
            result.WarningMessages.Add($"还有 {incompleteSteps.Count} 个步骤未完成");
            // 这里可以根据业务需求决定是警告还是错误
        }

        return result;
    }

    /// <summary>
    /// 获取验证规则列表
    /// </summary>
    public IEnumerable<ValidationRule> GetValidationRules()
    {
        return _validationRules.AsReadOnly();
    }

    /// <summary>
    /// 验证实验基本信息
    /// </summary>
    private void ValidateExperimentBasicInfo(Experiment experiment, ValidationResult result)
    {
        // 应用验证规则
        foreach (var rule in _validationRules.Where(r => r.IsEnabled))
        {
            switch (rule.Type)
            {
                case ValidationRuleType.Required:
                    ValidateRequired(experiment, rule, result);
                    break;
                case ValidationRuleType.Length:
                    ValidateLength(experiment, rule, result);
                    break;
                case ValidationRuleType.Range:
                    ValidateRange(experiment, rule, result);
                    break;
                case ValidationRuleType.Format:
                    ValidateFormat(experiment, rule, result);
                    break;
                case ValidationRuleType.Custom:
                    ValidateCustom(experiment, rule, result);
                    break;
            }
        }
    }

    /// <summary>
    /// 验证实验业务规则
    /// </summary>
    private async Task ValidateExperimentBusinessRulesAsync(Experiment experiment, ValidationResult result)
    {
        // 业务规则1：同名实验检查
        var existingExperiments = await _experimentService.GetExperimentsByConditionAsync(
            e => e.Name == experiment.Name && e.Id != experiment.Id);
        
        if (existingExperiments.Any())
        {
            result.WarningMessages.Add($"已存在同名实验：{experiment.Name}");
        }

        // 业务规则2：优先级检查
        if (experiment.Priority > 5)
        {
            result.WarningMessages.Add("实验优先级过高，可能影响其他实验的执行");
        }

        // 业务规则3：实验类型与步骤匹配检查
        ValidateExperimentTypeStepConsistency(experiment, result);
    }

    /// <summary>
    /// 验证实验类型与步骤的一致性
    /// </summary>
    private void ValidateExperimentTypeStepConsistency(Experiment experiment, ValidationResult result)
    {
        // 根据实验类型验证步骤的合理性
        switch (experiment.Type)
        {
            case ExperimentType.Dilution:
                if (!experiment.Steps.Any(s => s.Type == StepType.Mixing))
                {
                    result.WarningMessages.Add("稀释实验建议包含混合步骤");
                }
                break;
            case ExperimentType.PhTest:
                if (!experiment.Steps.Any(s => s.Type == StepType.Measurement))
                {
                    result.ErrorMessages.Add("pH测试实验必须包含测量步骤");
                    result.IsValid = false;
                }
                break;
        }
    }

    /// <summary>
    /// 必填验证
    /// </summary>
    private void ValidateRequired(Experiment experiment, ValidationRule rule, ValidationResult result)
    {
        var fieldName = rule.Parameters.GetValueOrDefault("field")?.ToString();
        if (string.IsNullOrEmpty(fieldName)) return;

        var value = GetFieldValue(experiment, fieldName);
        if (value == null || (value is string str && string.IsNullOrWhiteSpace(str)))
        {
            AddValidationMessage(result, rule, $"{fieldName} 不能为空");
        }
    }

    /// <summary>
    /// 长度验证
    /// </summary>
    private void ValidateLength(Experiment experiment, ValidationRule rule, ValidationResult result)
    {
        var fieldName = rule.Parameters.GetValueOrDefault("field")?.ToString();
        var maxLength = Convert.ToInt32(rule.Parameters.GetValueOrDefault("maxLength", 0));
        
        if (string.IsNullOrEmpty(fieldName) || maxLength <= 0) return;

        var value = GetFieldValue(experiment, fieldName) as string;
        if (!string.IsNullOrEmpty(value) && value.Length > maxLength)
        {
            AddValidationMessage(result, rule, $"{fieldName} 长度不能超过 {maxLength} 个字符");
        }
    }

    /// <summary>
    /// 范围验证
    /// </summary>
    private void ValidateRange(Experiment experiment, ValidationRule rule, ValidationResult result)
    {
        var fieldName = rule.Parameters.GetValueOrDefault("field")?.ToString();
        var min = Convert.ToInt32(rule.Parameters.GetValueOrDefault("min", int.MinValue));
        var max = Convert.ToInt32(rule.Parameters.GetValueOrDefault("max", int.MaxValue));
        
        if (string.IsNullOrEmpty(fieldName)) return;

        var value = GetFieldValue(experiment, fieldName);
        if (value is int intValue && (intValue < min || intValue > max))
        {
            AddValidationMessage(result, rule, $"{fieldName} 必须在 {min} 到 {max} 之间");
        }
    }

    /// <summary>
    /// 格式验证
    /// </summary>
    private void ValidateFormat(Experiment experiment, ValidationRule rule, ValidationResult result)
    {
        // 这里可以实现正则表达式验证等格式检查
    }

    /// <summary>
    /// 自定义验证
    /// </summary>
    private void ValidateCustom(Experiment experiment, ValidationRule rule, ValidationResult result)
    {
        // 这里可以实现自定义验证逻辑
    }

    /// <summary>
    /// 获取字段值
    /// </summary>
    private object? GetFieldValue(Experiment experiment, string fieldName)
    {
        return fieldName.ToLower() switch
        {
            "name" => experiment.Name,
            "description" => experiment.Description,
            "priority" => experiment.Priority,
            _ => null
        };
    }

    /// <summary>
    /// 添加验证消息
    /// </summary>
    private void AddValidationMessage(ValidationResult result, ValidationRule rule, string message)
    {
        switch (rule.Level)
        {
            case ValidationLevel.Error:
            case ValidationLevel.Critical:
                result.IsValid = false;
                result.ErrorMessages.Add(message);
                break;
            case ValidationLevel.Warning:
                result.WarningMessages.Add(message);
                break;
        }
    }

    /// <summary>
    /// 初始化验证规则
    /// </summary>
    private void InitializeValidationRules()
    {
        _validationRules.AddRange(new[]
        {
            new ValidationRule
            {
                Id = "experiment-name-required",
                Name = "实验名称必填",
                Type = ValidationRuleType.Required,
                Level = ValidationLevel.Error,
                Parameters = new Dictionary<string, object> { ["field"] = "name" }
            },
            new ValidationRule
            {
                Id = "experiment-name-length",
                Name = "实验名称长度限制",
                Type = ValidationRuleType.Length,
                Level = ValidationLevel.Error,
                Parameters = new Dictionary<string, object> { ["field"] = "name", ["maxLength"] = 100 }
            },
            new ValidationRule
            {
                Id = "experiment-priority-range",
                Name = "实验优先级范围",
                Type = ValidationRuleType.Range,
                Level = ValidationLevel.Warning,
                Parameters = new Dictionary<string, object> { ["field"] = "priority", ["min"] = 1, ["max"] = 5 }
            }
        });
    }
}

<UserControl x:Class="LabDemo.App.Views.WelcomeView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:prism="http://prismlibrary.com/"
             prism:ViewModelLocator.AutoWireViewModel="True">
    
    <!-- 
    欢迎视图 - 展示应用程序的主要功能和统计信息
    这个视图展示了MVVM模式的基本应用
    -->
    
    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- 欢迎标题 -->
        <Border Grid.Row="0" Background="#1976D2" Padding="30,20">
            <StackPanel HorizontalAlignment="Center">
                <TextBlock Text="🔬" FontSize="48" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                <TextBlock Text="欢迎使用实验室管理系统" FontSize="24" FontWeight="Bold" 
                         Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="基于 Prism + MVVM + 事件聚合器 + 表达式树 构建" FontSize="14" 
                         Foreground="#E3F2FD" HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>
        
        <!-- 主要内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="40,30">
                
                <!-- 统计信息卡片 -->
                <TextBlock Text="系统概览" FontSize="20" FontWeight="Bold" Margin="0,0,0,20"/>
                
                <Grid Margin="0,0,0,30">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- 总实验数 -->
                    <Border Grid.Column="0" Background="#E3F2FD" CornerRadius="8" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="📊" FontSize="32" HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding TotalExperiments}" FontSize="28" FontWeight="Bold" 
                                     HorizontalAlignment="Center" Foreground="#1976D2"/>
                            <TextBlock Text="总实验数" FontSize="12" HorizontalAlignment="Center" 
                                     Foreground="#666" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- 运行中实验 -->
                    <Border Grid.Column="1" Background="#E8F5E8" CornerRadius="8" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="▶️" FontSize="32" HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding RunningExperiments}" FontSize="28" FontWeight="Bold" 
                                     HorizontalAlignment="Center" Foreground="#4CAF50"/>
                            <TextBlock Text="运行中" FontSize="12" HorizontalAlignment="Center" 
                                     Foreground="#666" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- 已完成实验 -->
                    <Border Grid.Column="2" Background="#F3E5F5" CornerRadius="8" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="✅" FontSize="32" HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding CompletedExperiments}" FontSize="28" FontWeight="Bold" 
                                     HorizontalAlignment="Center" Foreground="#9C27B0"/>
                            <TextBlock Text="已完成" FontSize="12" HorizontalAlignment="Center" 
                                     Foreground="#666" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- 失败实验 -->
                    <Border Grid.Column="3" Background="#FFEBEE" CornerRadius="8" Padding="20" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="❌" FontSize="32" HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding FailedExperiments}" FontSize="28" FontWeight="Bold" 
                                     HorizontalAlignment="Center" Foreground="#F44336"/>
                            <TextBlock Text="失败" FontSize="12" HorizontalAlignment="Center" 
                                     Foreground="#666" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                </Grid>
                
                <!-- 快速操作 -->
                <TextBlock Text="快速操作" FontSize="20" FontWeight="Bold" Margin="0,20,0,20"/>
                
                <Grid Margin="0,0,0,30">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <Button Grid.Column="0" Height="80" Margin="5" 
                          Command="{Binding CreateExperimentCommand}">
                        <StackPanel>
                            <TextBlock Text="➕" FontSize="24"/>
                            <TextBlock Text="创建实验" FontSize="14" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Grid.Column="1" Height="80" Margin="5" 
                          Command="{Binding ViewExperimentsCommand}">
                        <StackPanel>
                            <TextBlock Text="📋" FontSize="24"/>
                            <TextBlock Text="查看实验" FontSize="14" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Button>
                    
                    <Button Grid.Column="2" Height="80" Margin="5" 
                          Command="{Binding ViewReportsCommand}">
                        <StackPanel>
                            <TextBlock Text="📈" FontSize="24"/>
                            <TextBlock Text="查看报告" FontSize="14" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Button>
                </Grid>
                
                <!-- 技术特性介绍 -->
                <TextBlock Text="技术特性" FontSize="20" FontWeight="Bold" Margin="0,20,0,20"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,20,0">
                        <Border Background="#F9F9F9" CornerRadius="5" Padding="15" Margin="0,0,0,10">
                            <StackPanel>
                                <TextBlock Text="🏗️ Prism 框架" FontWeight="Bold" FontSize="14" Margin="0,0,0,5"/>
                                <TextBlock Text="• 依赖注入容器" FontSize="12" Margin="0,2"/>
                                <TextBlock Text="• 模块化架构" FontSize="12" Margin="0,2"/>
                                <TextBlock Text="• 区域导航系统" FontSize="12" Margin="0,2"/>
                                <TextBlock Text="• MVVM 模式支持" FontSize="12" Margin="0,2"/>
                            </StackPanel>
                        </Border>
                        
                        <Border Background="#F9F9F9" CornerRadius="5" Padding="15" Margin="0,0,0,10">
                            <StackPanel>
                                <TextBlock Text="📡 事件聚合器" FontWeight="Bold" FontSize="14" Margin="0,0,0,5"/>
                                <TextBlock Text="• 松耦合通信" FontSize="12" Margin="0,2"/>
                                <TextBlock Text="• 发布-订阅模式" FontSize="12" Margin="0,2"/>
                                <TextBlock Text="• 类型安全事件" FontSize="12" Margin="0,2"/>
                                <TextBlock Text="• 异步事件处理" FontSize="12" Margin="0,2"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1">
                        <Border Background="#F9F9F9" CornerRadius="5" Padding="15" Margin="0,0,0,10">
                            <StackPanel>
                                <TextBlock Text="🌳 表达式树" FontWeight="Bold" FontSize="14" Margin="0,0,0,5"/>
                                <TextBlock Text="• 动态查询构建" FontSize="12" Margin="0,2"/>
                                <TextBlock Text="• 类型安全查询" FontSize="12" Margin="0,2"/>
                                <TextBlock Text="• 智能提示支持" FontSize="12" Margin="0,2"/>
                                <TextBlock Text="• 可组合查询条件" FontSize="12" Margin="0,2"/>
                            </StackPanel>
                        </Border>
                        
                        <Border Background="#F9F9F9" CornerRadius="5" Padding="15" Margin="0,0,0,10">
                            <StackPanel>
                                <TextBlock Text="🎨 数据模板选择器" FontWeight="Bold" FontSize="14" Margin="0,0,0,5"/>
                                <TextBlock Text="• 动态UI模板" FontSize="12" Margin="0,2"/>
                                <TextBlock Text="• 类型驱动显示" FontSize="12" Margin="0,2"/>
                                <TextBlock Text="• 状态相关UI" FontSize="12" Margin="0,2"/>
                                <TextBlock Text="• 可扩展模板系统" FontSize="12" Margin="0,2"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Grid>
                
            </StackPanel>
        </ScrollViewer>
        
    </Grid>
    
</UserControl>

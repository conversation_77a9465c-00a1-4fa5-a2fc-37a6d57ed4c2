using System.Linq.Expressions;
using LabDemo.Domain.Entities;
using LabDemo.Domain.Enums;
using LabDemo.Service.Interfaces;

namespace LabDemo.Service.Implementations;

/// <summary>
/// 实验服务实现类
/// 
/// 为什么要有服务层？
/// 1. 业务逻辑封装：将复杂的业务规则封装在服务中
/// 2. 事务管理：确保数据操作的一致性
/// 3. 缓存管理：提高数据访问性能
/// 4. 权限控制：在服务层实现访问控制
/// 5. 日志记录：记录业务操作日志
/// </summary>
public class ExperimentService : IExperimentService
{
    // 模拟数据存储 - 在实际项目中这里会是数据库访问层
    private readonly List<Experiment> _experiments = new();
    private readonly object _lock = new object();

    public ExperimentService()
    {
        // 初始化一些示例数据
        InitializeSampleData();
    }

    /// <summary>
    /// 创建新实验
    /// </summary>
    public async Task<Experiment> CreateExperimentAsync(Experiment experiment)
    {
        // 业务验证
        if (string.IsNullOrWhiteSpace(experiment.Name))
            throw new ArgumentException("实验名称不能为空");

        // 设置创建信息
        experiment.Id = Guid.NewGuid().ToString();
        experiment.CreatedAt = DateTime.Now;
        experiment.Status = ExperimentStatus.Created;

        lock (_lock)
        {
            _experiments.Add(experiment);
        }

        // 模拟异步操作
        await Task.Delay(10);
        
        return experiment;
    }

    /// <summary>
    /// 根据ID获取实验
    /// </summary>
    public async Task<Experiment?> GetExperimentByIdAsync(string id)
    {
        await Task.Delay(10); // 模拟异步操作
        
        lock (_lock)
        {
            return _experiments.FirstOrDefault(e => e.Id == id && !e.IsDeleted);
        }
    }

    /// <summary>
    /// 获取所有实验
    /// </summary>
    public async Task<IEnumerable<Experiment>> GetAllExperimentsAsync()
    {
        await Task.Delay(10);
        
        lock (_lock)
        {
            return _experiments.Where(e => !e.IsDeleted).ToList();
        }
    }

    /// <summary>
    /// 根据条件查询实验 - 表达式树的核心应用
    /// 
    /// 什么是表达式树？
    /// 表达式树是将代码表示为数据的一种方式
    /// 它将Lambda表达式转换为可以在运行时分析和操作的树形结构
    /// 
    /// 表达式树的优势：
    /// 1. 类型安全：编译时检查，避免字符串拼接的错误
    /// 2. 智能提示：IDE可以提供完整的智能提示
    /// 3. 重构友好：重命名属性时会自动更新查询条件
    /// 4. 可转换：可以转换为SQL、MongoDB查询等
    /// 5. 可组合：可以动态组合复杂的查询条件
    /// </summary>
    public async Task<IEnumerable<Experiment>> GetExperimentsByConditionAsync(
        Expression<Func<Experiment, bool>> predicate)
    {
        await Task.Delay(10);
        
        // 编译表达式树为可执行的委托
        var compiledPredicate = predicate.Compile();
        
        lock (_lock)
        {
            return _experiments.Where(e => !e.IsDeleted && compiledPredicate(e)).ToList();
        }
    }

    /// <summary>
    /// 根据状态获取实验
    /// </summary>
    public async Task<IEnumerable<Experiment>> GetExperimentsByStatusAsync(ExperimentStatus status)
    {
        // 使用表达式树实现
        return await GetExperimentsByConditionAsync(e => e.Status == status);
    }

    /// <summary>
    /// 根据类型获取实验
    /// </summary>
    public async Task<IEnumerable<Experiment>> GetExperimentsByTypeAsync(ExperimentType type)
    {
        // 使用表达式树实现
        return await GetExperimentsByConditionAsync(e => e.Type == type);
    }

    /// <summary>
    /// 更新实验
    /// </summary>
    public async Task<Experiment> UpdateExperimentAsync(Experiment experiment)
    {
        lock (_lock)
        {
            var existing = _experiments.FirstOrDefault(e => e.Id == experiment.Id);
            if (existing == null)
                throw new ArgumentException($"实验不存在：{experiment.Id}");

            // 更新属性
            existing.Name = experiment.Name;
            existing.Description = experiment.Description;
            existing.Type = experiment.Type;
            existing.Priority = experiment.Priority;
            existing.UpdatedAt = DateTime.Now;
        }

        await Task.Delay(10);
        return experiment;
    }

    /// <summary>
    /// 删除实验（软删除）
    /// </summary>
    public async Task<bool> DeleteExperimentAsync(string id)
    {
        lock (_lock)
        {
            var experiment = _experiments.FirstOrDefault(e => e.Id == id);
            if (experiment == null) return false;

            experiment.IsDeleted = true;
            experiment.UpdatedAt = DateTime.Now;
        }

        await Task.Delay(10);
        return true;
    }

    /// <summary>
    /// 开始实验
    /// </summary>
    public async Task<bool> StartExperimentAsync(string id)
    {
        var experiment = await GetExperimentByIdAsync(id);
        if (experiment == null) return false;

        try
        {
            experiment.Start(); // 使用领域模型的业务方法
            await UpdateExperimentAsync(experiment);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 暂停实验
    /// </summary>
    public async Task<bool> PauseExperimentAsync(string id)
    {
        var experiment = await GetExperimentByIdAsync(id);
        if (experiment == null) return false;

        try
        {
            experiment.Pause();
            await UpdateExperimentAsync(experiment);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 恢复实验
    /// </summary>
    public async Task<bool> ResumeExperimentAsync(string id)
    {
        var experiment = await GetExperimentByIdAsync(id);
        if (experiment == null) return false;

        try
        {
            experiment.Resume();
            await UpdateExperimentAsync(experiment);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 完成实验
    /// </summary>
    public async Task<bool> CompleteExperimentAsync(string id, string result)
    {
        var experiment = await GetExperimentByIdAsync(id);
        if (experiment == null) return false;

        try
        {
            experiment.Complete(result);
            await UpdateExperimentAsync(experiment);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取实验统计信息 - 展示表达式树的高级用法
    /// </summary>
    public async Task<ExperimentStatistics> GetStatisticsAsync()
    {
        var allExperiments = await GetAllExperimentsAsync();
        var experimentList = allExperiments.ToList();

        // 使用表达式树进行复杂统计
        var statistics = new ExperimentStatistics
        {
            TotalExperiments = experimentList.Count,
            RunningExperiments = experimentList.Count(e => e.Status == ExperimentStatus.Running),
            CompletedExperiments = experimentList.Count(e => e.Status == ExperimentStatus.Completed),
            FailedExperiments = experimentList.Count(e => e.Status == ExperimentStatus.Failed),
        };

        // 计算平均执行时间
        var completedExperiments = experimentList.Where(e => e.Status == ExperimentStatus.Completed && e.Duration.HasValue);
        if (completedExperiments.Any())
        {
            statistics.AverageExecutionTimeMinutes = completedExperiments.Average(e => e.Duration!.Value.TotalMinutes);
        }

        // 按类型分组统计
        statistics.ExperimentsByType = experimentList
            .GroupBy(e => e.Type)
            .ToDictionary(g => g.Key, g => g.Count());

        // 按状态分组统计
        statistics.ExperimentsByStatus = experimentList
            .GroupBy(e => e.Status)
            .ToDictionary(g => g.Key, g => g.Count());

        return statistics;
    }

    /// <summary>
    /// 初始化示例数据
    /// </summary>
    private void InitializeSampleData()
    {
        var sampleExperiments = new[]
        {
            new Experiment
            {
                Id = Guid.NewGuid().ToString(),
                Name = "酸碱中和实验",
                Description = "测试盐酸和氢氧化钠的中和反应",
                Type = ExperimentType.Neutralization,
                Status = ExperimentStatus.Completed,
                Priority = 2,
                StartTime = DateTime.Now.AddHours(-2),
                EndTime = DateTime.Now.AddHours(-1),
                Result = "中和反应完成，pH值为7.0"
            },
            new Experiment
            {
                Id = Guid.NewGuid().ToString(),
                Name = "样品稀释实验",
                Description = "将浓度为10%的溶液稀释到1%",
                Type = ExperimentType.Dilution,
                Status = ExperimentStatus.Running,
                Priority = 3,
                StartTime = DateTime.Now.AddMinutes(-30)
            },
            new Experiment
            {
                Id = Guid.NewGuid().ToString(),
                Name = "pH值检测",
                Description = "检测未知溶液的pH值",
                Type = ExperimentType.PhTest,
                Status = ExperimentStatus.Created,
                Priority = 1
            }
        };

        _experiments.AddRange(sampleExperiments);
    }
}

using Prism.Events;
using LabDemo.Domain.Entities;
using LabDemo.Domain.Enums;

namespace LabDemo.Service.Events;

/// <summary>
/// 实验状态变化事件
/// </summary>
public class ExperimentStatusChangedEvent : PubSubEvent<ExperimentStatusChangedEventArgs>
{
}

/// <summary>
/// 实验状态变化事件参数
/// </summary>
public class ExperimentStatusChangedEventArgs
{
    /// <summary>
    /// 实验ID
    /// </summary>
    public string ExperimentId { get; set; } = string.Empty;

    /// <summary>
    /// 实验名称
    /// </summary>
    public string ExperimentName { get; set; } = string.Empty;

    /// <summary>
    /// 旧状态
    /// </summary>
    public ExperimentStatus OldStatus { get; set; }

    /// <summary>
    /// 新状态
    /// </summary>
    public ExperimentStatus NewStatus { get; set; }

    /// <summary>
    /// 状态变化时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;

    /// <summary>
    /// 变化原因或描述
    /// </summary>
    public string? Reason { get; set; }

    /// <summary>
    /// 操作用户
    /// </summary>
    public string? UserId { get; set; }
}

/// <summary>
/// 实验创建事件
/// </summary>
public class ExperimentCreatedEvent : PubSubEvent<ExperimentCreatedEventArgs>
{
}

/// <summary>
/// 实验创建事件参数
/// </summary>
public class ExperimentCreatedEventArgs
{
    /// <summary>
    /// 新创建的实验
    /// </summary>
    public Experiment Experiment { get; set; } = null!;

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建者
    /// </summary>
    public string? CreatedBy { get; set; }
}

/// <summary>
/// 实验删除事件
/// </summary>
public class ExperimentDeletedEvent : PubSubEvent<ExperimentDeletedEventArgs>
{
}

/// <summary>
/// 实验删除事件参数
/// </summary>
public class ExperimentDeletedEventArgs
{
    /// <summary>
    /// 被删除的实验ID
    /// </summary>
    public string ExperimentId { get; set; } = string.Empty;

    /// <summary>
    /// 被删除的实验名称
    /// </summary>
    public string ExperimentName { get; set; } = string.Empty;

    /// <summary>
    /// 删除时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;

    /// <summary>
    /// 删除者
    /// </summary>
    public string? DeletedBy { get; set; }

    /// <summary>
    /// 删除原因
    /// </summary>
    public string? Reason { get; set; }
}

/// <summary>
/// 实验进度更新事件
/// </summary>
public class ExperimentProgressUpdatedEvent : PubSubEvent<ExperimentProgressUpdatedEventArgs>
{
}

/// <summary>
/// 实验进度更新事件参数
/// </summary>
public class ExperimentProgressUpdatedEventArgs
{
    /// <summary>
    /// 实验ID
    /// </summary>
    public string ExperimentId { get; set; } = string.Empty;

    /// <summary>
    /// 当前步骤名称
    /// </summary>
    public string CurrentStepName { get; set; } = string.Empty;

    /// <summary>
    /// 总步骤数
    /// </summary>
    public int TotalSteps { get; set; }

    /// <summary>
    /// 已完成步骤数
    /// </summary>
    public int CompletedSteps { get; set; }

    /// <summary>
    /// 进度百分比 (0-100)
    /// </summary>
    public double ProgressPercentage { get; set; }

    /// <summary>
    /// 预计剩余时间
    /// </summary>
    public TimeSpan? EstimatedRemainingTime { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

/// <summary>
/// 系统通知事件
/// </summary>
public class SystemNotificationEvent : PubSubEvent<SystemNotificationEventArgs>
{
}

/// <summary>
/// 系统通知事件参数
/// </summary>
public class SystemNotificationEventArgs
{
    /// <summary>
    /// 通知标题
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// 通知消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 通知类型
    /// </summary>
    public NotificationType Type { get; set; }

    /// <summary>
    /// 通知时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;

    /// <summary>
    /// 是否自动关闭
    /// </summary>
    public bool AutoClose { get; set; } = true;

    /// <summary>
    /// 自动关闭延迟（秒）
    /// </summary>
    public int AutoCloseDelay { get; set; } = 5;
}

/// <summary>
/// 通知类型枚举
/// </summary>
public enum NotificationType
{
    /// <summary>
    /// 信息通知
    /// </summary>
    Information,

    /// <summary>
    /// 成功通知
    /// </summary>
    Success,

    /// <summary>
    /// 警告通知
    /// </summary>
    Warning,

    /// <summary>
    /// 错误通知
    /// </summary>
    Error
}

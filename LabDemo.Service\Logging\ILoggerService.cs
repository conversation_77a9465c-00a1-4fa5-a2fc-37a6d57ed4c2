namespace LabDemo.Service.Logging;

/// <summary>
/// 日志服务接口
/// 
/// 为什么需要日志服务？
/// 1. 问题诊断：记录应用程序运行时的关键信息
/// 2. 性能监控：跟踪应用程序的性能指标
/// 3. 安全审计：记录用户操作和安全相关事件
/// 4. 业务分析：收集业务数据用于分析
/// 5. 调试支持：帮助开发人员定位和解决问题
/// </summary>
public interface ILoggerService
{
    /// <summary>
    /// 记录调试信息
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogDebug(string message, params object[] args);

    /// <summary>
    /// 记录一般信息
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogInformation(string message, params object[] args);

    /// <summary>
    /// 记录警告信息
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogWarning(string message, params object[] args);

    /// <summary>
    /// 记录错误信息
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogError(string message, params object[] args);

    /// <summary>
    /// 记录错误信息（包含异常）
    /// </summary>
    /// <param name="exception">异常对象</param>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogError(Exception exception, string message, params object[] args);

    /// <summary>
    /// 记录严重错误信息
    /// </summary>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogCritical(string message, params object[] args);

    /// <summary>
    /// 记录严重错误信息（包含异常）
    /// </summary>
    /// <param name="exception">异常对象</param>
    /// <param name="message">日志消息</param>
    /// <param name="args">格式化参数</param>
    void LogCritical(Exception exception, string message, params object[] args);

    /// <summary>
    /// 记录业务操作日志
    /// </summary>
    /// <param name="operation">操作名称</param>
    /// <param name="userId">用户ID</param>
    /// <param name="details">操作详情</param>
    void LogBusinessOperation(string operation, string userId, string details);

    /// <summary>
    /// 记录性能指标
    /// </summary>
    /// <param name="metricName">指标名称</param>
    /// <param name="value">指标值</param>
    /// <param name="unit">单位</param>
    void LogPerformanceMetric(string metricName, double value, string unit = "");

    /// <summary>
    /// 开始性能计时
    /// </summary>
    /// <param name="operationName">操作名称</param>
    /// <returns>性能计时器</returns>
    IDisposable BeginPerformanceTimer(string operationName);
}

/// <summary>
/// 日志级别枚举
/// </summary>
public enum LogLevel
{
    /// <summary>
    /// 调试级别 - 详细的调试信息
    /// </summary>
    Debug = 0,

    /// <summary>
    /// 信息级别 - 一般的信息消息
    /// </summary>
    Information = 1,

    /// <summary>
    /// 警告级别 - 警告消息，不影响程序运行
    /// </summary>
    Warning = 2,

    /// <summary>
    /// 错误级别 - 错误消息，可能影响功能
    /// </summary>
    Error = 3,

    /// <summary>
    /// 严重级别 - 严重错误，可能导致程序崩溃
    /// </summary>
    Critical = 4
}

/// <summary>
/// 日志条目
/// </summary>
public class LogEntry
{
    /// <summary>
    /// 日志ID
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;

    /// <summary>
    /// 日志级别
    /// </summary>
    public LogLevel Level { get; set; }

    /// <summary>
    /// 日志消息
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// 异常信息
    /// </summary>
    public Exception? Exception { get; set; }

    /// <summary>
    /// 日志类别
    /// </summary>
    public string Category { get; set; } = string.Empty;

    /// <summary>
    /// 用户ID
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// 操作名称
    /// </summary>
    public string? Operation { get; set; }

    /// <summary>
    /// 附加属性
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();

    /// <summary>
    /// 线程ID
    /// </summary>
    public int ThreadId { get; set; } = Thread.CurrentThread.ManagedThreadId;

    /// <summary>
    /// 机器名称
    /// </summary>
    public string MachineName { get; set; } = Environment.MachineName;
}

/// <summary>
/// 性能计时器接口
/// </summary>
public interface IPerformanceTimer : IDisposable
{
    /// <summary>
    /// 操作名称
    /// </summary>
    string OperationName { get; }

    /// <summary>
    /// 开始时间
    /// </summary>
    DateTime StartTime { get; }

    /// <summary>
    /// 已用时间
    /// </summary>
    TimeSpan ElapsedTime { get; }
}

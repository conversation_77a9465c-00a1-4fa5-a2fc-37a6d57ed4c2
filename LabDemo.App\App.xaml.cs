using System.Windows;
using Prism.Ioc;
using Prism.Modularity;
using LabDemo.App.Views;
using LabDemo.App.ViewModels;
using LabDemo.Service.Interfaces;
using LabDemo.Service.Implementations;
using LabDemo.Service.Configuration;
using LabDemo.Service.Logging;
using Prism.Events;
using Prism.DryIoc;

namespace LabDemo.App;

/// <summary>
/// App.xaml 的交互逻辑
/// 
/// 为什么继承PrismApplication而不是Application？
/// 1. 依赖注入：PrismApplication提供了内置的DI容器
/// 2. 模块化：支持模块的动态加载和卸载
/// 3. 事件聚合器：提供松耦合的组件间通信
/// 4. 导航系统：支持区域导航和视图注入
/// 5. 命令系统：提供DelegateCommand等高级命令
/// </summary>
public partial class App : PrismApplication
{
    /// <summary>
    /// 创建Shell窗口
    /// 
    /// 什么是Shell？
    /// Shell是应用程序的主窗口容器，它定义了应用程序的整体布局结构
    /// 通常包含：菜单栏、工具栏、状态栏、主内容区域等
    /// 
    /// 为什么要重写CreateShell？
    /// 1. 控制主窗口的创建时机
    /// 2. 确保依赖注入容器已经配置完成
    /// 3. 可以在窗口显示前进行额外的初始化工作
    /// </summary>
    /// <returns>主窗口实例</returns>
    protected override Window CreateShell()
    {
        // 通过依赖注入容器解析主窗口
        // Container.Resolve<T>() 是依赖注入的核心方法
        // 它会：
        // 1. 查找T类型的注册信息
        // 2. 创建T的实例
        // 3. 自动注入T所需的依赖项
        // 4. 返回完全初始化的对象
        return Container.Resolve<MainWindow>();
    }

    /// <summary>
    /// 注册类型到依赖注入容器
    /// 
    /// 什么是依赖注入？
    /// 依赖注入(DI)是一种设计模式，用于实现控制反转(IoC)
    /// 它的核心思想是：不要在类内部创建依赖对象，而是从外部注入
    /// 
    /// 依赖注入的好处：
    /// 1. 松耦合：类不直接依赖具体实现，而是依赖抽象接口
    /// 2. 可测试：可以轻松注入Mock对象进行单元测试
    /// 3. 可配置：可以在运行时决定使用哪种实现
    /// 4. 可维护：修改实现不影响使用方
    /// </summary>
    /// <param name="containerRegistry">容器注册器</param>
    protected override void RegisterTypes(IContainerRegistry containerRegistry)
    {
        // 注册服务 - 接口到实现的映射
        containerRegistry.Register<IExperimentService, ExperimentService>();
        containerRegistry.Register<IEventPublisher, EventPublisher>();

        // 注册配置和日志服务（单例）
        containerRegistry.RegisterSingleton<IConfigurationService, ConfigurationService>();
        containerRegistry.RegisterSingleton<ILoggerService, SimpleLoggerService>();

        // 注册单例服务
        containerRegistry.RegisterSingleton<IEventAggregator, EventAggregator>();

        // 注册视图和视图模型
        containerRegistry.RegisterForNavigation<MainWindow, MainWindowViewModel>();
        containerRegistry.RegisterForNavigation<WelcomeView, WelcomeViewModel>();
        containerRegistry.RegisterForNavigation<ExperimentListView, ExperimentListViewModel>();
    }

    /// <summary>
    /// 配置模块目录
    /// </summary>
    /// <param name="moduleCatalog">模块目录</param>
    protected override void ConfigureModuleCatalog(IModuleCatalog moduleCatalog)
    {
        // 添加实验模块
        moduleCatalog.AddModule<LabDemo.ExperimentModule.ExperimentModule>();

        base.ConfigureModuleCatalog(moduleCatalog);
    }

    /// <summary>
    /// 应用程序启动时的初始化
    /// </summary>
    /// <param name="e">启动事件参数</param>
    protected override void OnStartup(StartupEventArgs e)
    {
        // 注册全局异常处理
        // 为什么需要全局异常处理？
        // 1. 用户体验：避免程序崩溃，提供友好的错误提示
        // 2. 问题诊断：记录异常信息，便于问题排查
        // 3. 数据安全：确保异常不会导致数据丢失
        this.DispatcherUnhandledException += OnDispatcherUnhandledException;
        AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;

        base.OnStartup(e);
    }

    /// <summary>
    /// 处理UI线程未处理的异常
    /// </summary>
    private void OnDispatcherUnhandledException(object sender, 
        System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
    {
        // 记录异常
        LogException(e.Exception, "UI线程异常");
        
        // 显示用户友好的错误消息
        MessageBox.Show($"应用程序发生错误：{e.Exception.Message}", 
            "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        
        // 标记异常已处理，防止程序崩溃
        e.Handled = true;
    }

    /// <summary>
    /// 处理非UI线程未处理的异常
    /// </summary>
    private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        if (e.ExceptionObject is Exception ex)
        {
            LogException(ex, "非UI线程异常");
        }
    }

    /// <summary>
    /// 记录异常信息
    /// </summary>
    /// <param name="exception">异常对象</param>
    /// <param name="source">异常来源</param>
    private void LogException(Exception exception, string source)
    {
        // 这里可以集成日志框架（如NLog、Serilog等）
        // 记录到文件、数据库或远程日志服务
        Console.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {source}: {exception}");
    }
}

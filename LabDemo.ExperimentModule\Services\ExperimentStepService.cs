using LabDemo.Domain.Entities;
using LabDemo.Domain.Enums;
using LabDemo.Service.Interfaces;
using Prism.Events;
using LabDemo.Service.Events;

namespace LabDemo.ExperimentModule.Services;

/// <summary>
/// 实验步骤服务实现
/// 
/// 这个类展示了模块内服务的实现方式：
/// 1. 依赖注入：通过构造函数注入需要的依赖
/// 2. 事件发布：通过事件聚合器发布业务事件
/// 3. 异步操作：使用async/await处理异步业务逻辑
/// 4. 错误处理：统一的错误处理和日志记录
/// </summary>
public class ExperimentStepService : IExperimentStepService
{
    private readonly IEventAggregator _eventAggregator;
    private readonly IEventPublisher _eventPublisher;
    private readonly List<ExperimentStepTemplate> _stepTemplates = new();
    private readonly Dictionary<string, StepProgress> _stepProgressCache = new();

    public ExperimentStepService(
        IEventAggregator eventAggregator,
        IEventPublisher eventPublisher)
    {
        _eventAggregator = eventAggregator;
        _eventPublisher = eventPublisher;
    }

    /// <summary>
    /// 初始化服务
    /// </summary>
    public void Initialize()
    {
        // 初始化步骤模板
        InitializeStepTemplates();
        
        Console.WriteLine("实验步骤服务初始化完成");
    }

    /// <summary>
    /// 获取指定类型的步骤模板
    /// </summary>
    public async Task<IEnumerable<ExperimentStepTemplate>> GetStepTemplatesAsync(StepType stepType)
    {
        await Task.Delay(10); // 模拟异步操作
        
        return _stepTemplates.Where(t => t.StepType == stepType).ToList();
    }

    /// <summary>
    /// 创建实验步骤
    /// </summary>
    public async Task<ExperimentStep> CreateStepAsync(string experimentId, StepType stepType, Dictionary<string, object> parameters)
    {
        // 验证参数
        var validationResult = await ValidateStepParametersAsync(stepType, parameters);
        if (!validationResult.IsValid)
        {
            throw new ArgumentException($"步骤参数验证失败: {string.Join(", ", validationResult.ErrorMessages)}");
        }

        // 获取步骤模板
        var template = _stepTemplates.FirstOrDefault(t => t.StepType == stepType);
        if (template == null)
        {
            throw new InvalidOperationException($"未找到步骤类型 {stepType} 的模板");
        }

        // 创建步骤
        var step = new ExperimentStep
        {
            Id = Guid.NewGuid().ToString(),
            ExperimentId = experimentId,
            Name = template.Name,
            Description = template.Description,
            Type = stepType,
            EstimatedDurationMinutes = template.EstimatedDurationMinutes,
            Parameters = System.Text.Json.JsonSerializer.Serialize(parameters),
            Status = StepStatus.Pending,
            CreatedAt = DateTime.Now
        };

        // 这里应该保存到数据库
        // await _repository.AddAsync(step);

        // 发布步骤创建事件
        _eventPublisher.PublishInfoNotification($"步骤 '{step.Name}' 创建成功");

        return step;
    }

    /// <summary>
    /// 执行实验步骤
    /// </summary>
    public async Task<StepExecutionResult> ExecuteStepAsync(string stepId)
    {
        var startTime = DateTime.Now;
        
        try
        {
            // 初始化进度
            var progress = new StepProgress
            {
                StepId = stepId,
                Status = StepStatus.Running,
                ProgressPercentage = 0,
                CurrentOperation = "开始执行步骤"
            };
            _stepProgressCache[stepId] = progress;

            // 发布进度更新事件
            PublishProgressUpdate(progress);

            // 模拟步骤执行过程
            await SimulateStepExecution(stepId);

            // 执行完成
            progress.Status = StepStatus.Completed;
            progress.ProgressPercentage = 100;
            progress.CurrentOperation = "步骤执行完成";
            PublishProgressUpdate(progress);

            var executionTime = DateTime.Now - startTime;
            
            return new StepExecutionResult
            {
                IsSuccess = true,
                Result = "步骤执行成功",
                ExecutionTime = executionTime,
                OutputData = new Dictionary<string, object>
                {
                    ["executionTime"] = executionTime.TotalSeconds,
                    ["timestamp"] = DateTime.Now
                }
            };
        }
        catch (Exception ex)
        {
            // 更新进度为失败状态
            if (_stepProgressCache.TryGetValue(stepId, out var progress))
            {
                progress.Status = StepStatus.Failed;
                progress.CurrentOperation = $"执行失败: {ex.Message}";
                PublishProgressUpdate(progress);
            }

            var executionTime = DateTime.Now - startTime;
            
            return new StepExecutionResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ExecutionTime = executionTime
            };
        }
    }

    /// <summary>
    /// 验证步骤参数
    /// </summary>
    public async Task<ValidationResult> ValidateStepParametersAsync(StepType stepType, Dictionary<string, object> parameters)
    {
        await Task.Delay(10); // 模拟异步验证
        
        var result = new ValidationResult { IsValid = true };
        
        // 获取步骤模板
        var template = _stepTemplates.FirstOrDefault(t => t.StepType == stepType);
        if (template == null)
        {
            result.IsValid = false;
            result.ErrorMessages.Add($"未找到步骤类型 {stepType} 的模板");
            return result;
        }

        // 验证必需参数
        foreach (var paramDef in template.ParameterDefinitions.Where(p => p.IsRequired))
        {
            if (!parameters.ContainsKey(paramDef.Name) || parameters[paramDef.Name] == null)
            {
                result.IsValid = false;
                result.ErrorMessages.Add($"缺少必需参数: {paramDef.DisplayName}");
            }
        }

        // 验证参数类型
        foreach (var param in parameters)
        {
            var paramDef = template.ParameterDefinitions.FirstOrDefault(p => p.Name == param.Key);
            if (paramDef != null && param.Value != null)
            {
                if (!paramDef.ParameterType.IsAssignableFrom(param.Value.GetType()))
                {
                    result.IsValid = false;
                    result.ErrorMessages.Add($"参数 {paramDef.DisplayName} 类型不匹配，期望 {paramDef.ParameterType.Name}");
                }
            }
        }

        return result;
    }

    /// <summary>
    /// 获取步骤执行进度
    /// </summary>
    public async Task<StepProgress> GetStepProgressAsync(string stepId)
    {
        await Task.Delay(10);
        
        if (_stepProgressCache.TryGetValue(stepId, out var progress))
        {
            return progress;
        }

        return new StepProgress
        {
            StepId = stepId,
            Status = StepStatus.Pending,
            ProgressPercentage = 0,
            CurrentOperation = "等待执行"
        };
    }

    /// <summary>
    /// 初始化步骤模板
    /// </summary>
    private void InitializeStepTemplates()
    {
        _stepTemplates.AddRange(new[]
        {
            new ExperimentStepTemplate
            {
                Id = "heating-template",
                Name = "加热步骤",
                Description = "将样品加热到指定温度",
                StepType = StepType.Heating,
                EstimatedDurationMinutes = 15,
                ParameterDefinitions = new List<StepParameterDefinition>
                {
                    new() { Name = "targetTemperature", DisplayName = "目标温度", ParameterType = typeof(double), IsRequired = true },
                    new() { Name = "heatingRate", DisplayName = "加热速率", ParameterType = typeof(double), DefaultValue = 5.0 },
                    new() { Name = "holdTime", DisplayName = "保持时间(分钟)", ParameterType = typeof(int), DefaultValue = 5 }
                }
            },
            new ExperimentStepTemplate
            {
                Id = "cooling-template",
                Name = "冷却步骤",
                Description = "将样品冷却到指定温度",
                StepType = StepType.Cooling,
                EstimatedDurationMinutes = 20,
                ParameterDefinitions = new List<StepParameterDefinition>
                {
                    new() { Name = "targetTemperature", DisplayName = "目标温度", ParameterType = typeof(double), IsRequired = true },
                    new() { Name = "coolingRate", DisplayName = "冷却速率", ParameterType = typeof(double), DefaultValue = 3.0 }
                }
            },
            new ExperimentStepTemplate
            {
                Id = "mixing-template",
                Name = "混合步骤",
                Description = "搅拌混合样品",
                StepType = StepType.Mixing,
                EstimatedDurationMinutes = 10,
                ParameterDefinitions = new List<StepParameterDefinition>
                {
                    new() { Name = "speed", DisplayName = "搅拌速度(rpm)", ParameterType = typeof(int), IsRequired = true },
                    new() { Name = "duration", DisplayName = "搅拌时间(分钟)", ParameterType = typeof(int), IsRequired = true },
                    new() { Name = "direction", DisplayName = "搅拌方向", ParameterType = typeof(string), DefaultValue = "顺时针" }
                }
            }
        });
    }

    /// <summary>
    /// 模拟步骤执行过程
    /// </summary>
    private async Task SimulateStepExecution(string stepId)
    {
        var random = new Random();
        var totalSteps = 10;
        
        for (int i = 1; i <= totalSteps; i++)
        {
            // 模拟执行时间
            await Task.Delay(random.Next(500, 1500));
            
            // 更新进度
            if (_stepProgressCache.TryGetValue(stepId, out var progress))
            {
                progress.ProgressPercentage = (double)i / totalSteps * 100;
                progress.CurrentOperation = $"执行子步骤 {i}/{totalSteps}";
                progress.EstimatedRemainingTime = TimeSpan.FromSeconds((totalSteps - i) * 1.0);
                
                PublishProgressUpdate(progress);
            }
        }
    }

    /// <summary>
    /// 发布进度更新事件
    /// </summary>
    private void PublishProgressUpdate(StepProgress progress)
    {
        // 这里可以发布步骤进度更新事件
        // _eventAggregator.GetEvent<StepProgressUpdatedEvent>().Publish(progress);
        
        Console.WriteLine($"步骤 {progress.StepId}: {progress.ProgressPercentage:F1}% - {progress.CurrentOperation}");
    }
}

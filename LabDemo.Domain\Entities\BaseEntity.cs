using System.ComponentModel.DataAnnotations;

namespace LabDemo.Domain.Entities;

/// <summary>
/// 基础实体类 - 为什么需要它？
/// 1. 统一管理所有实体的公共属性（ID、创建时间等）
/// 2. 提供统一的数据验证规则
/// 3. 简化数据库映射配置
/// </summary>
public abstract class BaseEntity
{
    /// <summary>
    /// 主键ID - 使用GUID确保分布式环境下的唯一性
    /// </summary>
    [Key]
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 创建时间 - 审计字段，记录数据创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 创建者 - 审计字段，记录是谁创建的数据
    /// </summary>
    public string? CreatedBy { get; set; }

    /// <summary>
    /// 更新时间 - 审计字段，记录最后修改时间
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// 更新者 - 审计字段，记录是谁修改的数据
    /// </summary>
    public string? UpdatedBy { get; set; }

    /// <summary>
    /// 软删除标记 - 不真正删除数据，只标记为已删除
    /// 好处：1. 数据安全 2. 可恢复 3. 保持数据完整性
    /// </summary>
    public bool IsDeleted { get; set; } = false;
}

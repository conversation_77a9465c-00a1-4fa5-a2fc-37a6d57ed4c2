namespace LabDemo.Domain.Enums;

/// <summary>
/// 实验类型枚举
/// 为什么使用枚举？
/// 1. 类型安全：编译时检查，避免无效值
/// 2. 可读性：代码更易理解
/// 3. 维护性：集中管理所有可能的值
/// 4. 性能：比字符串比较更高效
/// </summary>
public enum ExperimentType
{
    /// <summary>
    /// 稀释实验 - 将样品稀释到指定浓度
    /// </summary>
    Dilution = 1,

    /// <summary>
    /// 中和实验 - 酸碱中和反应
    /// </summary>
    Neutralization = 2,

    /// <summary>
    /// 检测实验 - 成分分析检测
    /// </summary>
    Detection = 3,

    /// <summary>
    /// 过滤实验 - 分离固体和液体
    /// </summary>
    Filtration = 4,

    /// <summary>
    /// pH测试 - 酸碱度测定
    /// </summary>
    PhTest = 5,

    /// <summary>
    /// 混合实验 - 多种物质混合
    /// </summary>
    Mixing = 6
}

/// <summary>
/// 实验状态枚举
/// 为什么需要状态管理？
/// 1. 生命周期管理：跟踪实验从创建到完成的整个过程
/// 2. 业务规则：确保状态转换的合法性
/// 3. 用户界面：根据状态显示不同的操作按钮
/// 4. 报告统计：按状态分组统计实验数据
/// </summary>
public enum ExperimentStatus
{
    /// <summary>
    /// 已创建 - 实验刚创建，还未开始执行
    /// </summary>
    Created = 0,

    /// <summary>
    /// 运行中 - 实验正在执行
    /// </summary>
    Running = 1,

    /// <summary>
    /// 已暂停 - 实验被暂时停止，可以恢复
    /// </summary>
    Paused = 2,

    /// <summary>
    /// 已完成 - 实验成功完成
    /// </summary>
    Completed = 3,

    /// <summary>
    /// 已失败 - 实验执行失败
    /// </summary>
    Failed = 4,

    /// <summary>
    /// 已取消 - 实验被用户取消
    /// </summary>
    Cancelled = 5
}

/// <summary>
/// 步骤类型枚举
/// 为什么需要步骤类型？
/// 1. 策略模式：不同类型的步骤有不同的执行逻辑
/// 2. 工厂模式：根据类型创建相应的执行器
/// 3. 模板选择：UI根据类型显示不同的模板
/// </summary>
public enum StepType
{
    /// <summary>
    /// 准备步骤 - 实验前的准备工作
    /// </summary>
    Preparation = 1,

    /// <summary>
    /// 加热步骤 - 加热到指定温度
    /// </summary>
    Heating = 2,

    /// <summary>
    /// 冷却步骤 - 冷却到指定温度
    /// </summary>
    Cooling = 3,

    /// <summary>
    /// 混合步骤 - 搅拌混合
    /// </summary>
    Mixing = 4,

    /// <summary>
    /// 等待步骤 - 等待指定时间
    /// </summary>
    Waiting = 5,

    /// <summary>
    /// 测量步骤 - 测量各种参数
    /// </summary>
    Measurement = 6,

    /// <summary>
    /// 清洁步骤 - 清洁设备
    /// </summary>
    Cleaning = 7
}

/// <summary>
/// 步骤状态枚举
/// </summary>
public enum StepStatus
{
    /// <summary>
    /// 待执行 - 步骤还未开始
    /// </summary>
    Pending = 0,

    /// <summary>
    /// 执行中 - 步骤正在执行
    /// </summary>
    Running = 1,

    /// <summary>
    /// 已完成 - 步骤成功完成
    /// </summary>
    Completed = 2,

    /// <summary>
    /// 已失败 - 步骤执行失败
    /// </summary>
    Failed = 3,

    /// <summary>
    /// 已跳过 - 步骤被跳过
    /// </summary>
    Skipped = 4
}

/// <summary>
/// 优先级枚举
/// 为什么需要优先级？
/// 1. 任务调度：高优先级任务优先执行
/// 2. 资源分配：优先级高的获得更多资源
/// 3. 用户体验：重要任务快速响应
/// </summary>
public enum Priority
{
    /// <summary>
    /// 低优先级
    /// </summary>
    Low = 1,

    /// <summary>
    /// 普通优先级
    /// </summary>
    Normal = 2,

    /// <summary>
    /// 高优先级
    /// </summary>
    High = 3,

    /// <summary>
    /// 紧急优先级
    /// </summary>
    Urgent = 4
}

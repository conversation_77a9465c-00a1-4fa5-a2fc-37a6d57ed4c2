using System.Diagnostics;
using LabDemo.Service.Configuration;

namespace LabDemo.Service.Logging;

/// <summary>
/// 简单日志服务实现
/// 
/// 这个类展示了一个基本的日志服务实现：
/// 1. 多目标输出：支持控制台和文件输出
/// 2. 日志级别过滤：根据配置过滤日志级别
/// 3. 格式化输出：统一的日志格式
/// 4. 性能计时：支持性能监控
/// 5. 线程安全：支持多线程环境
/// </summary>
public class SimpleLoggerService : ILoggerService
{
    private readonly IConfigurationService _configurationService;
    private readonly object _lockObject = new object();
    private LoggingConfiguration _config;

    public SimpleLoggerService(IConfigurationService configurationService)
    {
        _configurationService = configurationService;
        _config = _configurationService.GetLoggingConfiguration();
        
        // 监听配置变更
        _configurationService.ConfigurationChanged += OnConfigurationChanged;
        
        // 确保日志目录存在
        EnsureLogDirectoryExists();
    }

    /// <summary>
    /// 记录调试信息
    /// </summary>
    public void LogDebug(string message, params object[] args)
    {
        Log(LogLevel.Debug, message, null, args);
    }

    /// <summary>
    /// 记录一般信息
    /// </summary>
    public void LogInformation(string message, params object[] args)
    {
        Log(LogLevel.Information, message, null, args);
    }

    /// <summary>
    /// 记录警告信息
    /// </summary>
    public void LogWarning(string message, params object[] args)
    {
        Log(LogLevel.Warning, message, null, args);
    }

    /// <summary>
    /// 记录错误信息
    /// </summary>
    public void LogError(string message, params object[] args)
    {
        Log(LogLevel.Error, message, null, args);
    }

    /// <summary>
    /// 记录错误信息（包含异常）
    /// </summary>
    public void LogError(Exception exception, string message, params object[] args)
    {
        Log(LogLevel.Error, message, exception, args);
    }

    /// <summary>
    /// 记录严重错误信息
    /// </summary>
    public void LogCritical(string message, params object[] args)
    {
        Log(LogLevel.Critical, message, null, args);
    }

    /// <summary>
    /// 记录严重错误信息（包含异常）
    /// </summary>
    public void LogCritical(Exception exception, string message, params object[] args)
    {
        Log(LogLevel.Critical, message, exception, args);
    }

    /// <summary>
    /// 记录业务操作日志
    /// </summary>
    public void LogBusinessOperation(string operation, string userId, string details)
    {
        var entry = new LogEntry
        {
            Level = LogLevel.Information,
            Message = $"业务操作: {operation}",
            Category = "Business",
            UserId = userId,
            Operation = operation
        };
        
        entry.Properties["Details"] = details;
        
        WriteLog(entry);
    }

    /// <summary>
    /// 记录性能指标
    /// </summary>
    public void LogPerformanceMetric(string metricName, double value, string unit = "")
    {
        var entry = new LogEntry
        {
            Level = LogLevel.Information,
            Message = $"性能指标: {metricName} = {value} {unit}",
            Category = "Performance"
        };
        
        entry.Properties["MetricName"] = metricName;
        entry.Properties["Value"] = value;
        entry.Properties["Unit"] = unit;
        
        WriteLog(entry);
    }

    /// <summary>
    /// 开始性能计时
    /// </summary>
    public IDisposable BeginPerformanceTimer(string operationName)
    {
        return new PerformanceTimer(operationName, this);
    }

    /// <summary>
    /// 核心日志记录方法
    /// </summary>
    private void Log(LogLevel level, string message, Exception? exception, params object[] args)
    {
        // 检查日志级别
        if (!ShouldLog(level))
            return;

        var entry = new LogEntry
        {
            Level = level,
            Message = args.Length > 0 ? string.Format(message, args) : message,
            Exception = exception,
            Category = "Application"
        };

        WriteLog(entry);
    }

    /// <summary>
    /// 写入日志
    /// </summary>
    private void WriteLog(LogEntry entry)
    {
        lock (_lockObject)
        {
            try
            {
                var formattedMessage = FormatLogEntry(entry);

                // 输出到控制台
                if (_config.EnableConsoleLogging)
                {
                    WriteToConsole(entry.Level, formattedMessage);
                }

                // 输出到文件
                if (_config.EnableFileLogging)
                {
                    WriteToFile(formattedMessage);
                }
            }
            catch (Exception ex)
            {
                // 日志记录失败时输出到控制台
                Console.WriteLine($"日志记录失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 格式化日志条目
    /// </summary>
    private string FormatLogEntry(LogEntry entry)
    {
        var level = entry.Level.ToString().ToUpper().PadRight(11);
        var timestamp = entry.Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff");
        var threadId = entry.ThreadId.ToString().PadLeft(3);
        
        var message = $"[{timestamp}] [{level}] [Thread-{threadId}] {entry.Message}";
        
        if (!string.IsNullOrEmpty(entry.Category))
        {
            message += $" [Category: {entry.Category}]";
        }
        
        if (!string.IsNullOrEmpty(entry.UserId))
        {
            message += $" [User: {entry.UserId}]";
        }
        
        if (!string.IsNullOrEmpty(entry.Operation))
        {
            message += $" [Operation: {entry.Operation}]";
        }

        if (entry.Exception != null)
        {
            message += $"\n异常信息: {entry.Exception}";
        }

        if (entry.Properties.Any())
        {
            message += "\n附加属性:";
            foreach (var prop in entry.Properties)
            {
                message += $"\n  {prop.Key}: {prop.Value}";
            }
        }

        return message;
    }

    /// <summary>
    /// 输出到控制台
    /// </summary>
    private void WriteToConsole(LogLevel level, string message)
    {
        var originalColor = Console.ForegroundColor;
        
        try
        {
            // 根据日志级别设置颜色
            Console.ForegroundColor = level switch
            {
                LogLevel.Debug => ConsoleColor.Gray,
                LogLevel.Information => ConsoleColor.White,
                LogLevel.Warning => ConsoleColor.Yellow,
                LogLevel.Error => ConsoleColor.Red,
                LogLevel.Critical => ConsoleColor.Magenta,
                _ => ConsoleColor.White
            };

            Console.WriteLine(message);
        }
        finally
        {
            Console.ForegroundColor = originalColor;
        }
    }

    /// <summary>
    /// 输出到文件
    /// </summary>
    private void WriteToFile(string message)
    {
        try
        {
            var logFile = GetCurrentLogFile();
            File.AppendAllText(logFile, message + Environment.NewLine);
            
            // 检查文件大小，如果超过限制则轮转
            CheckAndRotateLogFile(logFile);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"写入日志文件失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 获取当前日志文件路径
    /// </summary>
    private string GetCurrentLogFile()
    {
        var directory = Path.GetDirectoryName(_config.LogFilePath) ?? "logs";
        var fileName = Path.GetFileNameWithoutExtension(_config.LogFilePath);
        var extension = Path.GetExtension(_config.LogFilePath);
        
        var dateString = DateTime.Now.ToString("yyyyMMdd");
        return Path.Combine(directory, $"{fileName}_{dateString}{extension}");
    }

    /// <summary>
    /// 检查并轮转日志文件
    /// </summary>
    private void CheckAndRotateLogFile(string logFile)
    {
        if (!File.Exists(logFile))
            return;

        var fileInfo = new FileInfo(logFile);
        var maxSizeBytes = _config.MaxFileSizeMB * 1024 * 1024;

        if (fileInfo.Length > maxSizeBytes)
        {
            // 轮转日志文件
            var directory = Path.GetDirectoryName(logFile)!;
            var fileName = Path.GetFileNameWithoutExtension(logFile);
            var extension = Path.GetExtension(logFile);
            
            var timestamp = DateTime.Now.ToString("HHmmss");
            var rotatedFile = Path.Combine(directory, $"{fileName}_{timestamp}{extension}");
            
            File.Move(logFile, rotatedFile);
            
            // 清理旧的日志文件
            CleanupOldLogFiles(directory);
        }
    }

    /// <summary>
    /// 清理旧的日志文件
    /// </summary>
    private void CleanupOldLogFiles(string directory)
    {
        try
        {
            var logFiles = Directory.GetFiles(directory, "*.log")
                .Select(f => new FileInfo(f))
                .OrderByDescending(f => f.CreationTime)
                .Skip(_config.RetainedFileCount)
                .ToList();

            foreach (var file in logFiles)
            {
                file.Delete();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"清理旧日志文件失败: {ex.Message}");
        }
    }

    /// <summary>
    /// 检查是否应该记录指定级别的日志
    /// </summary>
    private bool ShouldLog(LogLevel level)
    {
        var configLevel = Enum.TryParse<LogLevel>(_config.LogLevel, true, out var result) 
            ? result 
            : LogLevel.Information;
        
        return level >= configLevel;
    }

    /// <summary>
    /// 确保日志目录存在
    /// </summary>
    private void EnsureLogDirectoryExists()
    {
        var directory = Path.GetDirectoryName(_config.LogFilePath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }
    }

    /// <summary>
    /// 配置变更事件处理
    /// </summary>
    private void OnConfigurationChanged(object? sender, ConfigurationChangedEventArgs e)
    {
        if (e.Key == "Logging")
        {
            _config = _configurationService.GetLoggingConfiguration();
            EnsureLogDirectoryExists();
        }
    }
}

/// <summary>
/// 性能计时器实现
/// </summary>
internal class PerformanceTimer : IPerformanceTimer
{
    private readonly Stopwatch _stopwatch;
    private readonly ILoggerService _logger;

    public string OperationName { get; }
    public DateTime StartTime { get; }
    public TimeSpan ElapsedTime => _stopwatch.Elapsed;

    public PerformanceTimer(string operationName, ILoggerService logger)
    {
        OperationName = operationName;
        StartTime = DateTime.Now;
        _logger = logger;
        _stopwatch = Stopwatch.StartNew();
    }

    public void Dispose()
    {
        _stopwatch.Stop();
        _logger.LogPerformanceMetric($"{OperationName}_Duration", _stopwatch.ElapsedMilliseconds, "ms");
    }
}

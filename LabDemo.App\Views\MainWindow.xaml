<Window x:Class="LabDemo.App.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:prism="http://prismlibrary.com/"
        xmlns:selectors="clr-namespace:LabDemo.App.Selectors"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        prism:ViewModelLocator.AutoWireViewModel="True"
        Title="{Binding Title}"
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen">
    
    <!-- 
    这个主窗口展示了完整的MVVM架构：
    1. 数据绑定：所有UI元素都绑定到ViewModel的属性
    2. 命令绑定：按钮等控件绑定到ViewModel的命令
    3. 区域管理：使用Prism的区域系统管理内容
    4. 模板选择：使用DataTemplateSelector显示不同类型的数据
    5. 样式和主题：统一的UI样式
    -->
    
    <Window.Resources>
        
        <!-- 通知模板选择器 -->
        <selectors:NotificationDataTemplateSelector x:Key="NotificationTemplateSelector">
            
            <!-- 信息通知模板 -->
            <selectors:NotificationDataTemplateSelector.InformationNotificationTemplate>
                <DataTemplate>
                    <Border Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="1" 
                            CornerRadius="3" Padding="10" Margin="2">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="{Binding Icon}" FontSize="16" 
                                     VerticalAlignment="Center" Margin="0,0,8,0"/>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{Binding Title}" FontWeight="Bold" FontSize="12"/>
                                <TextBlock Text="{Binding Message}" FontSize="11" TextWrapping="Wrap"/>
                                <TextBlock Text="{Binding FormattedTimestamp}" FontSize="10" 
                                         Foreground="Gray" Margin="0,2,0,0"/>
                            </StackPanel>
                            
                            <Button Grid.Column="2" Content="×" Width="20" Height="20" 
                                  Command="{Binding DataContext.CloseNotificationCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                  CommandParameter="{Binding}" Background="Transparent" BorderThickness="0"/>
                        </Grid>
                    </Border>
                </DataTemplate>
            </selectors:NotificationDataTemplateSelector.InformationNotificationTemplate>
            
            <!-- 成功通知模板 -->
            <selectors:NotificationDataTemplateSelector.SuccessNotificationTemplate>
                <DataTemplate>
                    <Border Background="#E8F5E8" BorderBrush="#4CAF50" BorderThickness="1" 
                            CornerRadius="3" Padding="10" Margin="2">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="{Binding Icon}" FontSize="16" 
                                     VerticalAlignment="Center" Margin="0,0,8,0"/>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{Binding Title}" FontWeight="Bold" FontSize="12"/>
                                <TextBlock Text="{Binding Message}" FontSize="11" TextWrapping="Wrap"/>
                                <TextBlock Text="{Binding FormattedTimestamp}" FontSize="10" 
                                         Foreground="Gray" Margin="0,2,0,0"/>
                            </StackPanel>
                            
                            <Button Grid.Column="2" Content="×" Width="20" Height="20" 
                                  Command="{Binding DataContext.CloseNotificationCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                  CommandParameter="{Binding}" Background="Transparent" BorderThickness="0"/>
                        </Grid>
                    </Border>
                </DataTemplate>
            </selectors:NotificationDataTemplateSelector.SuccessNotificationTemplate>
            
            <!-- 警告通知模板 -->
            <selectors:NotificationDataTemplateSelector.WarningNotificationTemplate>
                <DataTemplate>
                    <Border Background="#FFF8E1" BorderBrush="#FF9800" BorderThickness="1" 
                            CornerRadius="3" Padding="10" Margin="2">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="{Binding Icon}" FontSize="16" 
                                     VerticalAlignment="Center" Margin="0,0,8,0"/>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{Binding Title}" FontWeight="Bold" FontSize="12"/>
                                <TextBlock Text="{Binding Message}" FontSize="11" TextWrapping="Wrap"/>
                                <TextBlock Text="{Binding FormattedTimestamp}" FontSize="10" 
                                         Foreground="Gray" Margin="0,2,0,0"/>
                            </StackPanel>
                            
                            <Button Grid.Column="2" Content="×" Width="20" Height="20" 
                                  Command="{Binding DataContext.CloseNotificationCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                  CommandParameter="{Binding}" Background="Transparent" BorderThickness="0"/>
                        </Grid>
                    </Border>
                </DataTemplate>
            </selectors:NotificationDataTemplateSelector.WarningNotificationTemplate>
            
            <!-- 错误通知模板 -->
            <selectors:NotificationDataTemplateSelector.ErrorNotificationTemplate>
                <DataTemplate>
                    <Border Background="#FFEBEE" BorderBrush="#F44336" BorderThickness="1" 
                            CornerRadius="3" Padding="10" Margin="2">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="{Binding Icon}" FontSize="16" 
                                     VerticalAlignment="Center" Margin="0,0,8,0"/>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{Binding Title}" FontWeight="Bold" FontSize="12"/>
                                <TextBlock Text="{Binding Message}" FontSize="11" TextWrapping="Wrap"/>
                                <TextBlock Text="{Binding FormattedTimestamp}" FontSize="10" 
                                         Foreground="Gray" Margin="0,2,0,0"/>
                            </StackPanel>
                            
                            <Button Grid.Column="2" Content="×" Width="20" Height="20" 
                                  Command="{Binding DataContext.CloseNotificationCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                  CommandParameter="{Binding}" Background="Transparent" BorderThickness="0"/>
                        </Grid>
                    </Border>
                </DataTemplate>
            </selectors:NotificationDataTemplateSelector.ErrorNotificationTemplate>
            
        </selectors:NotificationDataTemplateSelector>
        
        <!-- 全局样式 -->
        <Style TargetType="Button">
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1976D2"/>
                </Trigger>
                <Trigger Property="IsEnabled" Value="False">
                    <Setter Property="Background" Value="#CCCCCC"/>
                    <Setter Property="Cursor" Value="Arrow"/>
                </Trigger>
            </Style.Triggers>
        </Style>
        
    </Window.Resources>
    
    <!-- 主布局 -->
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>  <!-- 标题栏 -->
            <RowDefinition Height="Auto"/>  <!-- 工具栏 -->
            <RowDefinition Height="*"/>     <!-- 主内容区 -->
            <RowDefinition Height="Auto"/>  <!-- 状态栏 -->
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="#1976D2" Padding="15,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="🔬" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <TextBlock Text="{Binding Title}" FontSize="20" FontWeight="Bold" 
                             Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="{Binding CurrentUser, StringFormat='当前用户: {0}'}" 
                             Foreground="White" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <Button Content="设置" Background="Transparent" Foreground="White" BorderThickness="1" BorderBrush="White"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- 工具栏 -->
        <Border Grid.Row="1" Background="#F5F5F5" Padding="10,8" BorderThickness="0,0,0,1" BorderBrush="#E0E0E0">
            <StackPanel Orientation="Horizontal">
                <Button Content="实验列表" Command="{Binding NavigateToExperimentListCommand}"/>
                <Button Content="新建实验" Command="{Binding CreateExperimentCommand}"/>
                <Button Content="刷新" Command="{Binding RefreshCommand}"/>
                <Separator Margin="10,0"/>
                <TextBlock Text="{Binding TotalExperimentsCount, StringFormat='总实验数: {0}'}" 
                         VerticalAlignment="Center" Margin="10,0"/>
                <TextBlock Text="{Binding RunningExperimentsCount, StringFormat='运行中: {0}'}" 
                         VerticalAlignment="Center" Margin="10,0" Foreground="#4CAF50"/>
            </StackPanel>
        </Border>
        
        <!-- 主内容区 -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>  <!-- 通知面板 -->
            </Grid.ColumnDefinitions>
            
            <!-- 主内容区域 - 使用Prism区域 -->
            <ContentControl Grid.Column="0" prism:RegionManager.RegionName="ContentRegion" 
                          Margin="10"/>
            
            <!-- 通知面板 -->
            <Border Grid.Column="1" Background="#FAFAFA" BorderThickness="1,0,0,0" BorderBrush="#E0E0E0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    
                    <!-- 通知标题 -->
                    <Border Grid.Row="0" Background="White" Padding="15,10" BorderThickness="0,0,0,1" BorderBrush="#E0E0E0">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="通知" FontWeight="Bold" FontSize="14"/>
                            <Button Grid.Column="1" Content="清除全部" FontSize="10" 
                                  Command="{Binding ClearNotificationsCommand}"
                                  Background="Transparent" Foreground="#666" Padding="5,2"/>
                        </Grid>
                    </Border>
                    
                    <!-- 通知列表 -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="5">
                        <ItemsControl ItemsSource="{Binding Notifications}" 
                                    ItemTemplateSelector="{StaticResource NotificationTemplateSelector}">
                            <ItemsControl.Template>
                                <ControlTemplate>
                                    <StackPanel IsItemsHost="True"/>
                                </ControlTemplate>
                            </ItemsControl.Template>
                        </ItemsControl>
                    </ScrollViewer>
                    
                    <!-- 最近实验 -->
                    <Border Grid.Row="2" Background="White" Padding="15,10" BorderThickness="0,1,0,0" BorderBrush="#E0E0E0">
                        <StackPanel>
                            <TextBlock Text="最近实验" FontWeight="Bold" FontSize="12" Margin="0,0,0,10"/>
                            <ItemsControl ItemsSource="{Binding RecentExperiments}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border Background="#F9F9F9" Padding="8" Margin="0,2" CornerRadius="3">
                                            <StackPanel>
                                                <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="11"/>
                                                <StackPanel Orientation="Horizontal" Margin="0,2,0,0">
                                                    <TextBlock Text="{Binding Status}" FontSize="10" 
                                                             Background="#E0E0E0" Padding="3,1" Margin="0,0,5,0"/>
                                                    <TextBlock Text="{Binding CreatedAt, StringFormat='{}{0:MM-dd HH:mm}'}" 
                                                             FontSize="10" Foreground="Gray"/>
                                                </StackPanel>
                                            </StackPanel>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>
        </Grid>
        
        <!-- 状态栏 -->
        <Border Grid.Row="3" Background="#F0F0F0" Padding="10,5" BorderThickness="0,1,0,0" BorderBrush="#E0E0E0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="就绪" VerticalAlignment="Center"/>
                    <TextBlock Text="{Binding BusyMessage}" VerticalAlignment="Center" Margin="10,0,0,0"
                             Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <ProgressBar Width="100" Height="15" IsIndeterminate="True" 
                               Visibility="{Binding IsBusy, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                    <TextBlock Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='{}{0:yyyy-MM-dd HH:mm:ss}'}" 
                             VerticalAlignment="Center" Margin="10,0,0,0" FontSize="11"/>
                </StackPanel>
            </Grid>
        </Border>
        
    </Grid>
    
</Window>

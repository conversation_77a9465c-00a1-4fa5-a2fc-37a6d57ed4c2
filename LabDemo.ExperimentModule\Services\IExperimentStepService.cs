using LabDemo.Domain.Entities;
using LabDemo.Domain.Enums;

namespace LabDemo.ExperimentModule.Services;

/// <summary>
/// 实验步骤服务接口
/// 
/// 为什么在模块中定义服务？
/// 1. 模块封装：服务只在模块内使用，不暴露给外部
/// 2. 业务聚合：将相关的业务逻辑聚合在一个模块中
/// 3. 依赖管理：模块可以独立管理自己的依赖
/// 4. 测试隔离：可以独立测试模块的功能
/// </summary>
public interface IExperimentStepService
{
    /// <summary>
    /// 初始化服务
    /// </summary>
    void Initialize();

    /// <summary>
    /// 获取指定类型的步骤模板
    /// </summary>
    /// <param name="stepType">步骤类型</param>
    /// <returns>步骤模板列表</returns>
    Task<IEnumerable<ExperimentStepTemplate>> GetStepTemplatesAsync(StepType stepType);

    /// <summary>
    /// 创建实验步骤
    /// </summary>
    /// <param name="experimentId">实验ID</param>
    /// <param name="stepType">步骤类型</param>
    /// <param name="parameters">步骤参数</param>
    /// <returns>创建的步骤</returns>
    Task<ExperimentStep> CreateStepAsync(string experimentId, StepType stepType, Dictionary<string, object> parameters);

    /// <summary>
    /// 执行实验步骤
    /// </summary>
    /// <param name="stepId">步骤ID</param>
    /// <returns>执行结果</returns>
    Task<StepExecutionResult> ExecuteStepAsync(string stepId);

    /// <summary>
    /// 验证步骤参数
    /// </summary>
    /// <param name="stepType">步骤类型</param>
    /// <param name="parameters">参数</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateStepParametersAsync(StepType stepType, Dictionary<string, object> parameters);

    /// <summary>
    /// 获取步骤执行进度
    /// </summary>
    /// <param name="stepId">步骤ID</param>
    /// <returns>执行进度</returns>
    Task<StepProgress> GetStepProgressAsync(string stepId);
}

/// <summary>
/// 实验步骤模板
/// 
/// 为什么需要步骤模板？
/// 1. 标准化：确保相同类型的步骤有一致的结构
/// 2. 复用性：可以重复使用相同的步骤配置
/// 3. 可配置：可以根据模板快速创建步骤
/// 4. 维护性：集中管理步骤的定义和参数
/// </summary>
public class ExperimentStepTemplate
{
    /// <summary>
    /// 模板ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 模板名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 模板描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 步骤类型
    /// </summary>
    public StepType StepType { get; set; }

    /// <summary>
    /// 预计执行时间（分钟）
    /// </summary>
    public int EstimatedDurationMinutes { get; set; }

    /// <summary>
    /// 参数定义
    /// </summary>
    public List<StepParameterDefinition> ParameterDefinitions { get; set; } = new();

    /// <summary>
    /// 默认参数值
    /// </summary>
    public Dictionary<string, object> DefaultParameters { get; set; } = new();
}

/// <summary>
/// 步骤参数定义
/// </summary>
public class StepParameterDefinition
{
    /// <summary>
    /// 参数名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 参数显示名称
    /// </summary>
    public string DisplayName { get; set; } = string.Empty;

    /// <summary>
    /// 参数类型
    /// </summary>
    public Type ParameterType { get; set; } = typeof(string);

    /// <summary>
    /// 是否必需
    /// </summary>
    public bool IsRequired { get; set; }

    /// <summary>
    /// 默认值
    /// </summary>
    public object? DefaultValue { get; set; }

    /// <summary>
    /// 验证规则
    /// </summary>
    public List<string> ValidationRules { get; set; } = new();

    /// <summary>
    /// 帮助文本
    /// </summary>
    public string? HelpText { get; set; }
}

/// <summary>
/// 步骤执行结果
/// </summary>
public class StepExecutionResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 执行结果
    /// </summary>
    public string? Result { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 执行时间
    /// </summary>
    public TimeSpan ExecutionTime { get; set; }

    /// <summary>
    /// 输出数据
    /// </summary>
    public Dictionary<string, object> OutputData { get; set; } = new();
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误消息列表
    /// </summary>
    public List<string> ErrorMessages { get; set; } = new();

    /// <summary>
    /// 警告消息列表
    /// </summary>
    public List<string> WarningMessages { get; set; } = new();
}

/// <summary>
/// 步骤进度
/// </summary>
public class StepProgress
{
    /// <summary>
    /// 步骤ID
    /// </summary>
    public string StepId { get; set; } = string.Empty;

    /// <summary>
    /// 当前状态
    /// </summary>
    public StepStatus Status { get; set; }

    /// <summary>
    /// 进度百分比 (0-100)
    /// </summary>
    public double ProgressPercentage { get; set; }

    /// <summary>
    /// 当前操作描述
    /// </summary>
    public string CurrentOperation { get; set; } = string.Empty;

    /// <summary>
    /// 预计剩余时间
    /// </summary>
    public TimeSpan? EstimatedRemainingTime { get; set; }

    /// <summary>
    /// 已用时间
    /// </summary>
    public TimeSpan ElapsedTime { get; set; }
}

using LabDemo.Domain.Entities;
using LabDemo.Domain.Enums;
using LabDemo.Service.Events;

namespace LabDemo.Service.Interfaces;

/// <summary>
/// 事件发布服务接口
/// 
/// 为什么需要事件发布服务？
/// 1. 职责分离：将事件发布逻辑从业务逻辑中分离
/// 2. 统一管理：集中管理所有事件的发布
/// 3. 可测试性：可以轻松Mock事件发布行为
/// 4. 可配置性：可以控制事件的发布策略
/// </summary>
public interface IEventPublisher
{
    /// <summary>
    /// 发布实验状态变化事件
    /// </summary>
    /// <param name="experimentId">实验ID</param>
    /// <param name="experimentName">实验名称</param>
    /// <param name="oldStatus">旧状态</param>
    /// <param name="newStatus">新状态</param>
    /// <param name="reason">变化原因</param>
    /// <param name="userId">操作用户</param>
    void PublishExperimentStatusChanged(string experimentId, string experimentName, 
        ExperimentStatus oldStatus, ExperimentStatus newStatus, 
        string? reason = null, string? userId = null);

    /// <summary>
    /// 发布实验创建事件
    /// </summary>
    /// <param name="experiment">新创建的实验</param>
    /// <param name="createdBy">创建者</param>
    void PublishExperimentCreated(Experiment experiment, string? createdBy = null);

    /// <summary>
    /// 发布实验删除事件
    /// </summary>
    /// <param name="experimentId">实验ID</param>
    /// <param name="experimentName">实验名称</param>
    /// <param name="deletedBy">删除者</param>
    /// <param name="reason">删除原因</param>
    void PublishExperimentDeleted(string experimentId, string experimentName, 
        string? deletedBy = null, string? reason = null);

    /// <summary>
    /// 发布实验进度更新事件
    /// </summary>
    /// <param name="experimentId">实验ID</param>
    /// <param name="currentStepName">当前步骤名称</param>
    /// <param name="totalSteps">总步骤数</param>
    /// <param name="completedSteps">已完成步骤数</param>
    /// <param name="estimatedRemainingTime">预计剩余时间</param>
    void PublishExperimentProgressUpdated(string experimentId, string currentStepName, 
        int totalSteps, int completedSteps, TimeSpan? estimatedRemainingTime = null);

    /// <summary>
    /// 发布系统通知事件
    /// </summary>
    /// <param name="title">通知标题</param>
    /// <param name="message">通知消息</param>
    /// <param name="type">通知类型</param>
    /// <param name="autoClose">是否自动关闭</param>
    /// <param name="autoCloseDelay">自动关闭延迟（秒）</param>
    void PublishSystemNotification(string title, string message, 
        NotificationType type = NotificationType.Information, 
        bool autoClose = true, int autoCloseDelay = 5);

    /// <summary>
    /// 发布成功通知
    /// </summary>
    /// <param name="message">消息内容</param>
    void PublishSuccessNotification(string message);

    /// <summary>
    /// 发布错误通知
    /// </summary>
    /// <param name="message">错误消息</param>
    void PublishErrorNotification(string message);

    /// <summary>
    /// 发布警告通知
    /// </summary>
    /// <param name="message">警告消息</param>
    void PublishWarningNotification(string message);

    /// <summary>
    /// 发布信息通知
    /// </summary>
    /// <param name="message">信息消息</param>
    void PublishInfoNotification(string message);
}

// 通知类型枚举已移动到 LabDemo.Service.Events 命名空间
// 使用 LabDemo.Service.Events.NotificationType

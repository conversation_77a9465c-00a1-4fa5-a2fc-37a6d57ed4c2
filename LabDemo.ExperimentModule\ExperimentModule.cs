using Prism.Ioc;
using Prism.Modularity;
using Prism.Regions;
using LabDemo.ExperimentModule.Services;

namespace LabDemo.ExperimentModule;

/// <summary>
/// 实验模块
/// 
/// 什么是Prism模块？
/// 模块是一个独立的功能单元，包含相关的视图、视图模型、服务等组件
/// 每个模块都实现IModule接口，负责自己的初始化和注册逻辑
/// 
/// 模块化的优势：
/// 1. 分离关注点：每个模块专注于特定的业务领域
/// 2. 团队协作：不同团队可以并行开发不同模块
/// 3. 按需加载：可以根据需要动态加载模块，减少启动时间
/// 4. 可插拔：可以轻松添加或移除功能模块
/// 5. 测试隔离：每个模块可以独立测试
/// 6. 部署灵活：可以独立部署和更新模块
/// </summary>
public class ExperimentModule : IModule
{
    /// <summary>
    /// 注册类型到依赖注入容器
    /// 
    /// 这个方法在模块加载时被调用，用于注册模块内的所有类型
    /// 包括：视图、视图模型、服务、对话框等
    /// </summary>
    /// <param name="containerRegistry">容器注册器</param>
    public void RegisterTypes(IContainerRegistry containerRegistry)
    {
        // 注册模块特定的服务
        // 为什么在模块中注册服务？
        // 1. 封装性：模块内部的服务只在模块内使用
        // 2. 避免冲突：不同模块可以有同名的服务
        // 3. 生命周期管理：模块可以控制服务的生命周期
        containerRegistry.Register<IExperimentStepService, ExperimentStepService>();
        containerRegistry.Register<IExperimentValidationService, ExperimentValidationService>();

        // 注册视图和视图模型用于导航
        // 注意：这里暂时注释掉视图注册，因为视图文件还未创建
        // 在实际项目中，这些视图应该存在
        // containerRegistry.RegisterForNavigation<ExperimentListView, ExperimentListViewModel>();
        // containerRegistry.RegisterForNavigation<ExperimentDetailView, ExperimentDetailViewModel>();
        // containerRegistry.RegisterForNavigation<ExperimentStepView, ExperimentStepViewModel>();

        // 注册对话框
        // 注意：这里暂时注释掉对话框注册，因为对话框文件还未创建
        // containerRegistry.RegisterDialog<CreateExperimentDialog, CreateExperimentDialogViewModel>();
        // containerRegistry.RegisterDialog<EditExperimentDialog, EditExperimentDialogViewModel>();
        // containerRegistry.RegisterDialog<ExperimentStepDialog, ExperimentStepDialogViewModel>();

        // 注册视图模型（如果需要在其他地方直接注入）
        // containerRegistry.Register<ExperimentListViewModel>();
        // containerRegistry.Register<ExperimentDetailViewModel>();
        // containerRegistry.Register<ExperimentStepViewModel>();
    }

    /// <summary>
    /// 模块初始化
    /// 
    /// 这个方法在所有模块的RegisterTypes方法执行完毕后被调用
    /// 用于执行模块的初始化逻辑，如注册菜单项、初始化数据等
    /// </summary>
    /// <param name="containerProvider">容器提供者</param>
    public void OnInitialized(IContainerProvider containerProvider)
    {
        // 获取区域管理器
        var regionManager = containerProvider.Resolve<IRegionManager>();

        // 注册视图到区域
        // 这里可以将视图注册到特定的区域，实现模块化的UI布局
        // regionManager.RegisterViewWithRegion("ExperimentRegion", typeof(ExperimentListView));

        // 初始化模块特定的数据或服务
        InitializeModuleData(containerProvider);

        // 注册菜单项（如果有主菜单系统）
        RegisterMenuItems(containerProvider);
    }

    /// <summary>
    /// 初始化模块数据
    /// </summary>
    /// <param name="containerProvider">容器提供者</param>
    private void InitializeModuleData(IContainerProvider containerProvider)
    {
        // 这里可以执行模块特定的初始化逻辑
        // 例如：
        // 1. 初始化数据库表
        // 2. 加载配置文件
        // 3. 启动后台服务
        // 4. 注册事件处理器

        try
        {
            // 示例：初始化实验步骤服务
            var stepService = containerProvider.Resolve<IExperimentStepService>();
            stepService.Initialize();

            // 示例：初始化验证服务
            var validationService = containerProvider.Resolve<IExperimentValidationService>();
            validationService.LoadValidationRules();

            Console.WriteLine("实验模块初始化完成");
        }
        catch (Exception ex)
        {
            // 记录初始化错误
            Console.WriteLine($"实验模块初始化失败: {ex.Message}");
            // 在实际应用中，这里应该使用日志框架记录错误
        }
    }

    /// <summary>
    /// 注册菜单项
    /// </summary>
    /// <param name="containerProvider">容器提供者</param>
    private void RegisterMenuItems(IContainerProvider containerProvider)
    {
        // 这里可以注册模块的菜单项
        // 在实际应用中，可能会有一个菜单服务来管理菜单项
        
        // 示例代码（需要实际的菜单服务接口）:
        /*
        var menuService = containerProvider.Resolve<IMenuService>();
        
        menuService.AddMenuItem(new MenuItem
        {
            Header = "实验管理",
            Items = new[]
            {
                new MenuItem { Header = "实验列表", Command = "NavigateToExperimentList" },
                new MenuItem { Header = "创建实验", Command = "CreateExperiment" },
                new MenuItem { Header = "实验统计", Command = "ShowExperimentStatistics" }
            }
        });
        */
    }
}

/// <summary>
/// 模块信息特性
/// 
/// 这个特性提供了模块的元数据信息
/// 可以用于模块的发现、加载顺序控制等
/// </summary>
[Module(ModuleName = "ExperimentModule")]
[ModuleDependency("CoreModule")] // 如果依赖其他模块，可以在这里声明
public class ExperimentModuleInfo : ExperimentModule
{
    // 这个类继承自ExperimentModule，主要用于添加模块元数据
    // 在实际应用中，可以根据需要添加更多的模块信息
}

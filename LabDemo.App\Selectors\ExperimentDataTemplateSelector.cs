using System.Windows;
using System.Windows.Controls;
using LabDemo.Domain.Entities;
using LabDemo.Domain.Enums;

namespace LabDemo.App.Selectors;

/// <summary>
/// 实验数据模板选择器
/// 
/// 什么是DataTemplateSelector？
/// DataTemplateSelector是WPF中的一个抽象类，用于根据数据对象的类型或属性
/// 动态选择合适的DataTemplate来渲染UI
/// 
/// 为什么需要DataTemplateSelector？
/// 1. 动态UI：根据数据类型显示不同的UI布局
/// 2. 类型安全：编译时确定模板选择逻辑
/// 3. 可维护性：集中管理模板选择逻辑
/// 4. 灵活性：可以根据复杂条件选择模板
/// 5. 性能优化：避免在XAML中使用复杂的触发器
/// 
/// 使用场景：
/// - 列表中显示不同类型的项目
/// - 根据状态显示不同的UI
/// - 根据用户权限显示不同的操作按钮
/// - 根据数据完整性显示不同的编辑界面
/// </summary>
public class ExperimentDataTemplateSelector : DataTemplateSelector
{
    /// <summary>
    /// 稀释实验模板
    /// 在XAML中设置，用于显示稀释类型的实验
    /// </summary>
    public DataTemplate? DilutionExperimentTemplate { get; set; }

    /// <summary>
    /// 中和实验模板
    /// </summary>
    public DataTemplate? NeutralizationExperimentTemplate { get; set; }

    /// <summary>
    /// 检测实验模板
    /// </summary>
    public DataTemplate? DetectionExperimentTemplate { get; set; }

    /// <summary>
    /// 过滤实验模板
    /// </summary>
    public DataTemplate? FiltrationExperimentTemplate { get; set; }

    /// <summary>
    /// pH测试实验模板
    /// </summary>
    public DataTemplate? PhTestExperimentTemplate { get; set; }

    /// <summary>
    /// 混合实验模板
    /// </summary>
    public DataTemplate? MixingExperimentTemplate { get; set; }

    /// <summary>
    /// 默认实验模板
    /// 当没有匹配的特定模板时使用
    /// </summary>
    public DataTemplate? DefaultExperimentTemplate { get; set; }

    /// <summary>
    /// 运行中实验模板
    /// 用于显示正在运行的实验，可能包含进度条等
    /// </summary>
    public DataTemplate? RunningExperimentTemplate { get; set; }

    /// <summary>
    /// 已完成实验模板
    /// 用于显示已完成的实验，可能包含结果摘要
    /// </summary>
    public DataTemplate? CompletedExperimentTemplate { get; set; }

    /// <summary>
    /// 失败实验模板
    /// 用于显示失败的实验，可能包含错误信息
    /// </summary>
    public DataTemplate? FailedExperimentTemplate { get; set; }

    /// <summary>
    /// 选择数据模板的核心方法
    /// 
    /// 这个方法会在WPF需要渲染数据项时被调用
    /// 我们可以根据数据对象的类型、属性或状态来选择合适的模板
    /// </summary>
    /// <param name="item">要渲染的数据对象</param>
    /// <param name="container">包含数据对象的容器元素</param>
    /// <returns>选择的数据模板</returns>
    public override DataTemplate? SelectTemplate(object item, DependencyObject container)
    {
        // 检查数据对象是否为实验类型
        if (item is not Experiment experiment)
        {
            return DefaultExperimentTemplate;
        }

        // 策略1：根据实验状态选择模板
        // 这种策略优先考虑实验的当前状态，因为状态通常决定了用户可以执行的操作
        var statusTemplate = SelectTemplateByStatus(experiment);
        if (statusTemplate != null)
        {
            return statusTemplate;
        }

        // 策略2：根据实验类型选择模板
        // 如果没有特定的状态模板，则根据实验类型选择
        var typeTemplate = SelectTemplateByType(experiment);
        if (typeTemplate != null)
        {
            return typeTemplate;
        }

        // 策略3：根据复合条件选择模板
        // 可以根据多个属性的组合来选择模板
        var compositeTemplate = SelectTemplateByCompositeConditions(experiment);
        if (compositeTemplate != null)
        {
            return compositeTemplate;
        }

        // 默认模板
        return DefaultExperimentTemplate;
    }

    /// <summary>
    /// 根据实验状态选择模板
    /// 
    /// 为什么优先考虑状态？
    /// 1. 用户体验：用户最关心的是实验当前的状态
    /// 2. 操作相关：不同状态下可执行的操作不同
    /// 3. 视觉反馈：状态变化需要明显的视觉反馈
    /// </summary>
    /// <param name="experiment">实验对象</param>
    /// <returns>状态相关的模板</returns>
    private DataTemplate? SelectTemplateByStatus(Experiment experiment)
    {
        return experiment.Status switch
        {
            ExperimentStatus.Running => RunningExperimentTemplate,
            ExperimentStatus.Completed => CompletedExperimentTemplate,
            ExperimentStatus.Failed => FailedExperimentTemplate,
            _ => null // 其他状态不使用特殊模板
        };
    }

    /// <summary>
    /// 根据实验类型选择模板
    /// 
    /// 为什么需要类型特定的模板？
    /// 1. 专业性：不同类型的实验有不同的专业术语和参数
    /// 2. 操作差异：不同类型的实验有不同的操作流程
    /// 3. 数据展示：不同类型的实验需要展示不同的数据
    /// </summary>
    /// <param name="experiment">实验对象</param>
    /// <returns>类型相关的模板</returns>
    private DataTemplate? SelectTemplateByType(Experiment experiment)
    {
        return experiment.Type switch
        {
            ExperimentType.Dilution => DilutionExperimentTemplate,
            ExperimentType.Neutralization => NeutralizationExperimentTemplate,
            ExperimentType.Detection => DetectionExperimentTemplate,
            ExperimentType.Filtration => FiltrationExperimentTemplate,
            ExperimentType.PhTest => PhTestExperimentTemplate,
            ExperimentType.Mixing => MixingExperimentTemplate,
            _ => null
        };
    }

    /// <summary>
    /// 根据复合条件选择模板
    /// 
    /// 这个方法展示了如何根据多个属性的组合来选择模板
    /// 在实际应用中，可能需要考虑更复杂的业务逻辑
    /// </summary>
    /// <param name="experiment">实验对象</param>
    /// <returns>复合条件相关的模板</returns>
    private DataTemplate? SelectTemplateByCompositeConditions(Experiment experiment)
    {
        // 示例：高优先级且运行时间超过预期的实验使用特殊模板
        if (experiment.Priority >= 4 && experiment.Status == ExperimentStatus.Running)
        {
            var duration = experiment.Duration;
            if (duration.HasValue && duration.Value.TotalHours > 2)
            {
                // 可以返回一个特殊的"高优先级长时间运行"模板
                // return HighPriorityLongRunningTemplate;
            }
        }

        // 示例：包含多个步骤的复杂实验使用特殊模板
        if (experiment.Steps.Count > 5)
        {
            // 可以返回一个"复杂实验"模板，显示步骤概览
            // return ComplexExperimentTemplate;
        }

        // 示例：最近创建的实验使用特殊模板
        if (experiment.CreatedAt > DateTime.Now.AddDays(-1))
        {
            // 可以返回一个"新实验"模板，包含新建标识
            // return NewExperimentTemplate;
        }

        return null;
    }
}

/// <summary>
/// 实验步骤数据模板选择器
/// 
/// 这个选择器专门用于实验步骤的模板选择
/// 展示了如何为不同的数据类型创建专门的选择器
/// </summary>
public class ExperimentStepDataTemplateSelector : DataTemplateSelector
{
    /// <summary>
    /// 准备步骤模板
    /// </summary>
    public DataTemplate? PreparationStepTemplate { get; set; }

    /// <summary>
    /// 加热步骤模板
    /// </summary>
    public DataTemplate? HeatingStepTemplate { get; set; }

    /// <summary>
    /// 冷却步骤模板
    /// </summary>
    public DataTemplate? CoolingStepTemplate { get; set; }

    /// <summary>
    /// 混合步骤模板
    /// </summary>
    public DataTemplate? MixingStepTemplate { get; set; }

    /// <summary>
    /// 等待步骤模板
    /// </summary>
    public DataTemplate? WaitingStepTemplate { get; set; }

    /// <summary>
    /// 测量步骤模板
    /// </summary>
    public DataTemplate? MeasurementStepTemplate { get; set; }

    /// <summary>
    /// 清洁步骤模板
    /// </summary>
    public DataTemplate? CleaningStepTemplate { get; set; }

    /// <summary>
    /// 默认步骤模板
    /// </summary>
    public DataTemplate? DefaultStepTemplate { get; set; }

    /// <summary>
    /// 正在执行的步骤模板
    /// </summary>
    public DataTemplate? RunningStepTemplate { get; set; }

    /// <summary>
    /// 已完成的步骤模板
    /// </summary>
    public DataTemplate? CompletedStepTemplate { get; set; }

    /// <summary>
    /// 失败的步骤模板
    /// </summary>
    public DataTemplate? FailedStepTemplate { get; set; }

    public override DataTemplate? SelectTemplate(object item, DependencyObject container)
    {
        if (item is not ExperimentStep step)
        {
            return DefaultStepTemplate;
        }

        // 优先根据状态选择模板
        var statusTemplate = step.Status switch
        {
            StepStatus.Running => RunningStepTemplate,
            StepStatus.Completed => CompletedStepTemplate,
            StepStatus.Failed => FailedStepTemplate,
            _ => null
        };

        if (statusTemplate != null)
        {
            return statusTemplate;
        }

        // 根据步骤类型选择模板
        var typeTemplate = step.Type switch
        {
            StepType.Preparation => PreparationStepTemplate,
            StepType.Heating => HeatingStepTemplate,
            StepType.Cooling => CoolingStepTemplate,
            StepType.Mixing => MixingStepTemplate,
            StepType.Waiting => WaitingStepTemplate,
            StepType.Measurement => MeasurementStepTemplate,
            StepType.Cleaning => CleaningStepTemplate,
            _ => DefaultStepTemplate
        };

        return typeTemplate ?? DefaultStepTemplate;
    }
}

/// <summary>
/// 通知数据模板选择器
/// 
/// 用于根据通知类型选择不同的显示模板
/// </summary>
public class NotificationDataTemplateSelector : DataTemplateSelector
{
    /// <summary>
    /// 信息通知模板
    /// </summary>
    public DataTemplate? InformationNotificationTemplate { get; set; }

    /// <summary>
    /// 成功通知模板
    /// </summary>
    public DataTemplate? SuccessNotificationTemplate { get; set; }

    /// <summary>
    /// 警告通知模板
    /// </summary>
    public DataTemplate? WarningNotificationTemplate { get; set; }

    /// <summary>
    /// 错误通知模板
    /// </summary>
    public DataTemplate? ErrorNotificationTemplate { get; set; }

    /// <summary>
    /// 默认通知模板
    /// </summary>
    public DataTemplate? DefaultNotificationTemplate { get; set; }

    public override DataTemplate? SelectTemplate(object item, DependencyObject container)
    {
        if (item is not ViewModels.NotificationViewModel notification)
        {
            return DefaultNotificationTemplate;
        }

        return notification.Type switch
        {
            Service.Events.NotificationType.Information => InformationNotificationTemplate,
            Service.Events.NotificationType.Success => SuccessNotificationTemplate,
            Service.Events.NotificationType.Warning => WarningNotificationTemplate,
            Service.Events.NotificationType.Error => ErrorNotificationTemplate,
            _ => DefaultNotificationTemplate
        };
    }
}

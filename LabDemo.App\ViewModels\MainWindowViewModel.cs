using System.Collections.ObjectModel;
using Prism.Commands;
using Prism.Events;
using Prism.Regions;
using LabDemo.Service.Events;
using LabDemo.Service.Interfaces;
using LabDemo.Domain.Entities;

namespace LabDemo.App.ViewModels;

/// <summary>
/// 主窗口视图模型
/// 
/// 这个类展示了MVVM模式的核心概念：
/// 1. 数据绑定：属性与UI控件绑定
/// 2. 命令绑定：命令与按钮等控件绑定
/// 3. 事件订阅：订阅业务事件并更新UI
/// 4. 导航管理：控制不同视图的显示
/// </summary>
public class MainWindowViewModel : BaseViewModel
{
    private readonly IEventAggregator _eventAggregator;
    private readonly IRegionManager _regionManager;
    private readonly IExperimentService _experimentService;
    private readonly IEventPublisher _eventPublisher;

    /// <summary>
    /// 构造函数 - 展示依赖注入的使用
    /// 
    /// 为什么在构造函数中注入依赖？
    /// 1. 明确依赖：构造函数清楚地表明了类的依赖关系
    /// 2. 不可变性：依赖在对象创建后不能改变，提高安全性
    /// 3. 测试友好：可以轻松注入Mock对象进行测试
    /// 4. 失败快速：如果依赖不可用，对象创建时就会失败
    /// </summary>
    public MainWindowViewModel(
        IEventAggregator eventAggregator,
        IRegionManager regionManager,
        IExperimentService experimentService,
        IEventPublisher eventPublisher)
    {
        _eventAggregator = eventAggregator;
        _regionManager = regionManager;
        _experimentService = experimentService;
        _eventPublisher = eventPublisher;

        Title = "实验室管理系统";
        
        // 初始化集合
        Notifications = new ObservableCollection<NotificationViewModel>();
        RecentExperiments = new ObservableCollection<Experiment>();

        // 初始化命令
        InitializeCommands();
        
        // 订阅事件
        SubscribeToEvents();
        
        // 加载初始数据
        _ = LoadInitialDataAsync();

        // 导航到欢迎页面
        NavigateToWelcome();
    }

    #region 属性

    private ObservableCollection<NotificationViewModel> _notifications = new();
    /// <summary>
    /// 通知集合 - 展示ObservableCollection的使用
    /// 
    /// 为什么使用ObservableCollection？
    /// 1. 自动通知：集合变化时自动通知UI更新
    /// 2. 数据绑定：与ListBox、DataGrid等控件完美配合
    /// 3. 性能优化：只更新变化的项，而不是整个列表
    /// </summary>
    public ObservableCollection<NotificationViewModel> Notifications
    {
        get => _notifications;
        set => SetProperty(ref _notifications, value);
    }

    private ObservableCollection<Experiment> _recentExperiments = new();
    /// <summary>
    /// 最近的实验列表
    /// </summary>
    public ObservableCollection<Experiment> RecentExperiments
    {
        get => _recentExperiments;
        set => SetProperty(ref _recentExperiments, value);
    }

    private int _runningExperimentsCount;
    /// <summary>
    /// 运行中的实验数量
    /// </summary>
    public int RunningExperimentsCount
    {
        get => _runningExperimentsCount;
        set => SetProperty(ref _runningExperimentsCount, value);
    }

    private int _totalExperimentsCount;
    /// <summary>
    /// 总实验数量
    /// </summary>
    public int TotalExperimentsCount
    {
        get => _totalExperimentsCount;
        set => SetProperty(ref _totalExperimentsCount, value);
    }

    private string _currentUser = "管理员";
    /// <summary>
    /// 当前用户
    /// </summary>
    public string CurrentUser
    {
        get => _currentUser;
        set => SetProperty(ref _currentUser, value);
    }

    #endregion

    #region 命令

    /// <summary>
    /// 导航到实验列表的命令
    /// </summary>
    public DelegateCommand NavigateToExperimentListCommand { get; private set; } = null!;

    /// <summary>
    /// 创建新实验的命令
    /// </summary>
    public DelegateCommand CreateExperimentCommand { get; private set; } = null!;

    /// <summary>
    /// 刷新数据的命令
    /// </summary>
    public DelegateCommand RefreshCommand { get; private set; } = null!;

    /// <summary>
    /// 清除通知的命令
    /// </summary>
    public DelegateCommand ClearNotificationsCommand { get; private set; } = null!;

    /// <summary>
    /// 关闭通知的命令
    /// </summary>
    public DelegateCommand<NotificationViewModel> CloseNotificationCommand { get; private set; } = null!;

    #endregion

    #region 私有方法

    /// <summary>
    /// 初始化命令
    /// </summary>
    private void InitializeCommands()
    {
        // 使用基类的CreateCommand方法创建命令
        // 这样可以自动处理忙碌状态
        NavigateToExperimentListCommand = CreateCommand(NavigateToExperimentList);
        CreateExperimentCommand = CreateCommand(CreateExperiment);
        RefreshCommand = CreateCommand(async () => await RefreshDataAsync());
        ClearNotificationsCommand = CreateCommand(ClearNotifications, () => Notifications.Count > 0);
        
        // 带参数的命令
        CloseNotificationCommand = new DelegateCommand<NotificationViewModel>(CloseNotification);
    }

    /// <summary>
    /// 订阅事件 - 展示事件聚合器的订阅机制
    /// 
    /// 事件订阅的好处：
    /// 1. 松耦合：视图模型不需要直接引用业务服务
    /// 2. 实时更新：业务状态变化时UI自动更新
    /// 3. 多订阅者：多个组件可以同时订阅同一事件
    /// 4. 异步处理：可以异步处理事件，不阻塞UI
    /// </summary>
    private void SubscribeToEvents()
    {
        // 订阅实验状态变化事件
        _eventAggregator.GetEvent<ExperimentStatusChangedEvent>()
            .Subscribe(OnExperimentStatusChanged, ThreadOption.UIThread);

        // 订阅实验创建事件
        _eventAggregator.GetEvent<ExperimentCreatedEvent>()
            .Subscribe(OnExperimentCreated, ThreadOption.UIThread);

        // 订阅实验删除事件
        _eventAggregator.GetEvent<ExperimentDeletedEvent>()
            .Subscribe(OnExperimentDeleted, ThreadOption.UIThread);

        // 订阅系统通知事件
        _eventAggregator.GetEvent<SystemNotificationEvent>()
            .Subscribe(OnSystemNotification, ThreadOption.UIThread);

        // ThreadOption.UIThread 确保事件处理在UI线程上执行
        // 这样可以直接更新UI控件，而不需要使用Dispatcher
    }

    /// <summary>
    /// 加载初始数据
    /// </summary>
    private async Task LoadInitialDataAsync()
    {
        await ExecuteAsync(async () =>
        {
            // 加载统计信息
            var statistics = await _experimentService.GetStatisticsAsync();
            TotalExperimentsCount = statistics.TotalExperiments;
            RunningExperimentsCount = statistics.RunningExperiments;

            // 加载最近的实验
            var experiments = await _experimentService.GetAllExperimentsAsync();
            var recentExperiments = experiments
                .OrderByDescending(e => e.CreatedAt)
                .Take(5)
                .ToList();

            RecentExperiments.Clear();
            foreach (var experiment in recentExperiments)
            {
                RecentExperiments.Add(experiment);
            }

        }, "正在加载数据...");
    }

    /// <summary>
    /// 刷新数据
    /// </summary>
    private async Task RefreshDataAsync()
    {
        await LoadInitialDataAsync();
        _eventPublisher.PublishInfoNotification("数据已刷新");
    }

    #endregion

    #region 命令处理方法

    /// <summary>
    /// 导航到欢迎页面
    /// </summary>
    private void NavigateToWelcome()
    {
        _regionManager.RequestNavigate("ContentRegion", "WelcomeView");
    }

    /// <summary>
    /// 导航到实验列表
    /// </summary>
    private void NavigateToExperimentList()
    {
        // 使用Prism的区域导航
        _regionManager.RequestNavigate("ContentRegion", "ExperimentListView");
    }

    /// <summary>
    /// 创建新实验
    /// </summary>
    private void CreateExperiment()
    {
        // 这里可以打开创建实验的对话框
        // 或者导航到创建实验的页面
        _eventPublisher.PublishInfoNotification("创建实验功能即将开放");
    }

    /// <summary>
    /// 清除所有通知
    /// </summary>
    private void ClearNotifications()
    {
        Notifications.Clear();
        // 通知命令重新评估可执行状态
        RaiseCanExecuteChanged();
    }

    /// <summary>
    /// 关闭指定通知
    /// </summary>
    private void CloseNotification(NotificationViewModel? notification)
    {
        if (notification != null && Notifications.Contains(notification))
        {
            Notifications.Remove(notification);
            RaiseCanExecuteChanged();
        }
    }

    #endregion

    #region 事件处理方法

    /// <summary>
    /// 处理实验状态变化事件
    /// </summary>
    private void OnExperimentStatusChanged(ExperimentStatusChangedEventArgs args)
    {
        // 更新统计信息
        _ = RefreshStatisticsAsync();

        // 更新最近实验列表中的实验状态
        var experiment = RecentExperiments.FirstOrDefault(e => e.Id == args.ExperimentId);
        if (experiment != null)
        {
            experiment.Status = args.NewStatus;
        }
    }

    /// <summary>
    /// 处理实验创建事件
    /// </summary>
    private void OnExperimentCreated(ExperimentCreatedEventArgs args)
    {
        // 将新实验添加到最近实验列表的开头
        RecentExperiments.Insert(0, args.Experiment);

        // 如果列表太长，移除最后一个
        if (RecentExperiments.Count > 5)
        {
            RecentExperiments.RemoveAt(RecentExperiments.Count - 1);
        }

        // 更新统计信息
        TotalExperimentsCount++;
    }

    /// <summary>
    /// 处理实验删除事件
    /// </summary>
    private void OnExperimentDeleted(ExperimentDeletedEventArgs args)
    {
        // 从最近实验列表中移除
        var experiment = RecentExperiments.FirstOrDefault(e => e.Id == args.ExperimentId);
        if (experiment != null)
        {
            RecentExperiments.Remove(experiment);
        }

        // 更新统计信息
        TotalExperimentsCount--;
    }

    /// <summary>
    /// 处理系统通知事件
    /// </summary>
    private void OnSystemNotification(SystemNotificationEventArgs args)
    {
        // 创建通知视图模型
        var notification = new NotificationViewModel
        {
            Title = args.Title,
            Message = args.Message,
            Type = args.Type,
            Timestamp = args.Timestamp,
            AutoClose = args.AutoClose,
            AutoCloseDelay = args.AutoCloseDelay
        };

        // 添加到通知列表
        Notifications.Insert(0, notification);

        // 如果设置了自动关闭，启动定时器
        if (notification.AutoClose)
        {
            _ = Task.Delay(TimeSpan.FromSeconds(notification.AutoCloseDelay))
                .ContinueWith(_ => 
                {
                    App.Current.Dispatcher.Invoke(() => 
                    {
                        if (Notifications.Contains(notification))
                        {
                            Notifications.Remove(notification);
                        }
                    });
                });
        }

        // 通知命令重新评估可执行状态
        RaiseCanExecuteChanged();
    }

    /// <summary>
    /// 刷新统计信息
    /// </summary>
    private async Task RefreshStatisticsAsync()
    {
        try
        {
            var statistics = await _experimentService.GetStatisticsAsync();
            TotalExperimentsCount = statistics.TotalExperiments;
            RunningExperimentsCount = statistics.RunningExperiments;
        }
        catch (Exception ex)
        {
            _eventPublisher.PublishErrorNotification($"刷新统计信息失败：{ex.Message}");
        }
    }

    #endregion
}

<UserControl x:Class="LabDemo.App.Views.ExperimentListView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:selectors="clr-namespace:LabDemo.App.Selectors"
             xmlns:enums="clr-namespace:LabDemo.Domain.Enums;assembly=LabDemo.Domain">
    
    <!-- 
    这个视图展示了如何使用DataTemplateSelector
    以及如何为不同类型的数据定义不同的显示模板
    -->
    
    <UserControl.Resources>
        
        <!-- 数据模板选择器实例 -->
        <selectors:ExperimentDataTemplateSelector x:Key="ExperimentTemplateSelector">
            
            <!-- 稀释实验模板 -->
            <selectors:ExperimentDataTemplateSelector.DilutionExperimentTemplate>
                <DataTemplate>
                    <Border Background="#E3F2FD" BorderBrush="#2196F3" BorderThickness="1" 
                            CornerRadius="5" Padding="10" Margin="5">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- 实验类型图标 -->
                            <TextBlock Grid.Column="0" Text="🧪" FontSize="24" 
                                     VerticalAlignment="Center" Margin="0,0,10,0"/>
                            
                            <!-- 实验信息 -->
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                                <TextBlock Text="{Binding Description}" FontSize="12" 
                                         Foreground="Gray" TextWrapping="Wrap"/>
                                <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                    <TextBlock Text="稀释实验" Background="#2196F3" 
                                             Foreground="White" Padding="5,2" FontSize="10"/>
                                    <TextBlock Text="{Binding Status}" Background="#4CAF50" 
                                             Foreground="White" Padding="5,2" FontSize="10" Margin="5,0,0,0"/>
                                </StackPanel>
                            </StackPanel>
                            
                            <!-- 操作按钮 -->
                            <StackPanel Grid.Column="2" Orientation="Horizontal">
                                <Button Content="查看" Padding="10,5" Margin="5,0"/>
                                <Button Content="编辑" Padding="10,5" Margin="5,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </DataTemplate>
            </selectors:ExperimentDataTemplateSelector.DilutionExperimentTemplate>
            
            <!-- 中和实验模板 -->
            <selectors:ExperimentDataTemplateSelector.NeutralizationExperimentTemplate>
                <DataTemplate>
                    <Border Background="#FFF3E0" BorderBrush="#FF9800" BorderThickness="1" 
                            CornerRadius="5" Padding="10" Margin="5">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="⚗️" FontSize="24" 
                                     VerticalAlignment="Center" Margin="0,0,10,0"/>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                                <TextBlock Text="{Binding Description}" FontSize="12" 
                                         Foreground="Gray" TextWrapping="Wrap"/>
                                <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                    <TextBlock Text="中和实验" Background="#FF9800" 
                                             Foreground="White" Padding="5,2" FontSize="10"/>
                                    <TextBlock Text="{Binding Status}" Background="#4CAF50" 
                                             Foreground="White" Padding="5,2" FontSize="10" Margin="5,0,0,0"/>
                                </StackPanel>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2" Orientation="Horizontal">
                                <Button Content="查看" Padding="10,5" Margin="5,0"/>
                                <Button Content="编辑" Padding="10,5" Margin="5,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </DataTemplate>
            </selectors:ExperimentDataTemplateSelector.NeutralizationExperimentTemplate>
            
            <!-- 运行中实验模板 -->
            <selectors:ExperimentDataTemplateSelector.RunningExperimentTemplate>
                <DataTemplate>
                    <Border Background="#E8F5E8" BorderBrush="#4CAF50" BorderThickness="2" 
                            CornerRadius="5" Padding="10" Margin="5">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <!-- 标题行 -->
                            <Grid Grid.Row="0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" Text="▶️" FontSize="20" 
                                         VerticalAlignment="Center" Margin="0,0,10,0"/>
                                
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                                    <TextBlock Text="正在运行..." FontSize="12" Foreground="#4CAF50"/>
                                </StackPanel>
                                
                                <StackPanel Grid.Column="2" Orientation="Horizontal">
                                    <Button Content="暂停" Padding="10,5" Margin="5,0"/>
                                    <Button Content="停止" Padding="10,5" Margin="5,0"/>
                                </StackPanel>
                            </Grid>
                            
                            <!-- 进度条 -->
                            <ProgressBar Grid.Row="1" Height="10" Margin="0,10,0,5" 
                                       Value="65" Maximum="100" Background="#E0E0E0" 
                                       Foreground="#4CAF50"/>
                            
                            <!-- 详细信息 -->
                            <Grid Grid.Row="2">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <TextBlock Grid.Column="0" Text="已用时间: 1h 30m" FontSize="10"/>
                                <TextBlock Grid.Column="1" Text="预计剩余: 45m" FontSize="10"/>
                                <TextBlock Grid.Column="2" Text="当前步骤: 混合" FontSize="10"/>
                            </Grid>
                        </Grid>
                    </Border>
                </DataTemplate>
            </selectors:ExperimentDataTemplateSelector.RunningExperimentTemplate>
            
            <!-- 已完成实验模板 -->
            <selectors:ExperimentDataTemplateSelector.CompletedExperimentTemplate>
                <DataTemplate>
                    <Border Background="#F3E5F5" BorderBrush="#9C27B0" BorderThickness="1" 
                            CornerRadius="5" Padding="10" Margin="5">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="✅" FontSize="24" 
                                     VerticalAlignment="Center" Margin="0,0,10,0"/>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                                <TextBlock Text="{Binding Result}" FontSize="12" 
                                         Foreground="Gray" TextWrapping="Wrap"/>
                                <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                    <TextBlock Text="已完成" Background="#4CAF50" 
                                             Foreground="White" Padding="5,2" FontSize="10"/>
                                    <TextBlock Text="{Binding EndTime, StringFormat='完成时间: {0:MM-dd HH:mm}'}" 
                                             FontSize="10" Margin="10,0,0,0"/>
                                </StackPanel>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2" Orientation="Horizontal">
                                <Button Content="查看报告" Padding="10,5" Margin="5,0"/>
                                <Button Content="重新运行" Padding="10,5" Margin="5,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </DataTemplate>
            </selectors:ExperimentDataTemplateSelector.CompletedExperimentTemplate>
            
            <!-- 失败实验模板 -->
            <selectors:ExperimentDataTemplateSelector.FailedExperimentTemplate>
                <DataTemplate>
                    <Border Background="#FFEBEE" BorderBrush="#F44336" BorderThickness="1" 
                            CornerRadius="5" Padding="10" Margin="5">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="❌" FontSize="24" 
                                     VerticalAlignment="Center" Margin="0,0,10,0"/>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                                <TextBlock Text="实验执行失败" FontSize="12" Foreground="#F44336"/>
                                <TextBlock Text="{Binding Result}" FontSize="11" 
                                         Foreground="Gray" TextWrapping="Wrap" Margin="0,2,0,0"/>
                                <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                    <TextBlock Text="失败" Background="#F44336" 
                                             Foreground="White" Padding="5,2" FontSize="10"/>
                                </StackPanel>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2" Orientation="Horizontal">
                                <Button Content="查看错误" Padding="10,5" Margin="5,0"/>
                                <Button Content="重试" Padding="10,5" Margin="5,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </DataTemplate>
            </selectors:ExperimentDataTemplateSelector.FailedExperimentTemplate>
            
            <!-- 默认实验模板 -->
            <selectors:ExperimentDataTemplateSelector.DefaultExperimentTemplate>
                <DataTemplate>
                    <Border Background="White" BorderBrush="#E0E0E0" BorderThickness="1" 
                            CornerRadius="5" Padding="10" Margin="5">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0" Text="🔬" FontSize="24" 
                                     VerticalAlignment="Center" Margin="0,0,10,0"/>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="{Binding Name}" FontWeight="Bold" FontSize="14"/>
                                <TextBlock Text="{Binding Description}" FontSize="12" 
                                         Foreground="Gray" TextWrapping="Wrap"/>
                                <StackPanel Orientation="Horizontal" Margin="0,5,0,0">
                                    <TextBlock Text="{Binding Type}" Background="#757575" 
                                             Foreground="White" Padding="5,2" FontSize="10"/>
                                    <TextBlock Text="{Binding Status}" Background="#9E9E9E" 
                                             Foreground="White" Padding="5,2" FontSize="10" Margin="5,0,0,0"/>
                                </StackPanel>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2" Orientation="Horizontal">
                                <Button Content="查看" Padding="10,5" Margin="5,0"/>
                                <Button Content="编辑" Padding="10,5" Margin="5,0"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                </DataTemplate>
            </selectors:ExperimentDataTemplateSelector.DefaultExperimentTemplate>
            
        </selectors:ExperimentDataTemplateSelector>
        
    </UserControl.Resources>
    
    <!-- 主要内容区域 -->
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- 标题和工具栏 -->
        <Border Grid.Row="0" Background="#F5F5F5" Padding="10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="实验列表" FontSize="18" FontWeight="Bold" 
                         VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="新建实验" Padding="15,8" Margin="5,0"/>
                    <Button Content="刷新" Padding="15,8" Margin="5,0"/>
                    <Button Content="导出" Padding="15,8" Margin="5,0"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- 实验列表 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <ItemsControl ItemsSource="{Binding Experiments}" 
                         ItemTemplateSelector="{StaticResource ExperimentTemplateSelector}">
                <!-- 
                这里是关键：使用ItemTemplateSelector属性
                WPF会为每个数据项调用选择器的SelectTemplate方法
                根据返回的DataTemplate来渲染UI
                -->
            </ItemsControl>
        </ScrollViewer>
        
    </Grid>
    
</UserControl>

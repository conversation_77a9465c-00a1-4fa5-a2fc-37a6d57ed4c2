using Prism.Commands;
using Prism.Events;
using Prism.Regions;
using LabDemo.Service.Interfaces;

namespace LabDemo.App.ViewModels;

/// <summary>
/// 欢迎页面视图模型
/// 
/// 这个视图模型展示了MVVM模式的基本应用：
/// 1. 数据绑定：统计信息的显示
/// 2. 命令绑定：快速操作按钮
/// 3. 服务注入：获取业务数据
/// </summary>
public class WelcomeViewModel : BaseViewModel
{
    private readonly IExperimentService _experimentService;
    private readonly IRegionManager _regionManager;
    private readonly IEventPublisher _eventPublisher;

    public WelcomeViewModel(
        IExperimentService experimentService,
        IRegionManager regionManager,
        IEventPublisher eventPublisher)
    {
        _experimentService = experimentService;
        _regionManager = regionManager;
        _eventPublisher = eventPublisher;

        Title = "欢迎";

        // 初始化命令
        InitializeCommands();

        // 加载统计数据
        _ = LoadStatisticsAsync();
    }

    #region 属性

    private int _totalExperiments;
    /// <summary>
    /// 总实验数
    /// </summary>
    public int TotalExperiments
    {
        get => _totalExperiments;
        set => SetProperty(ref _totalExperiments, value);
    }

    private int _runningExperiments;
    /// <summary>
    /// 运行中的实验数
    /// </summary>
    public int RunningExperiments
    {
        get => _runningExperiments;
        set => SetProperty(ref _runningExperiments, value);
    }

    private int _completedExperiments;
    /// <summary>
    /// 已完成的实验数
    /// </summary>
    public int CompletedExperiments
    {
        get => _completedExperiments;
        set => SetProperty(ref _completedExperiments, value);
    }

    private int _failedExperiments;
    /// <summary>
    /// 失败的实验数
    /// </summary>
    public int FailedExperiments
    {
        get => _failedExperiments;
        set => SetProperty(ref _failedExperiments, value);
    }

    #endregion

    #region 命令

    /// <summary>
    /// 创建实验命令
    /// </summary>
    public DelegateCommand CreateExperimentCommand { get; private set; } = null!;

    /// <summary>
    /// 查看实验命令
    /// </summary>
    public DelegateCommand ViewExperimentsCommand { get; private set; } = null!;

    /// <summary>
    /// 查看报告命令
    /// </summary>
    public DelegateCommand ViewReportsCommand { get; private set; } = null!;

    #endregion

    #region 私有方法

    /// <summary>
    /// 初始化命令
    /// </summary>
    private void InitializeCommands()
    {
        CreateExperimentCommand = CreateCommand(CreateExperiment);
        ViewExperimentsCommand = CreateCommand(ViewExperiments);
        ViewReportsCommand = CreateCommand(ViewReports);
    }

    /// <summary>
    /// 加载统计数据
    /// </summary>
    private async Task LoadStatisticsAsync()
    {
        await ExecuteAsync(async () =>
        {
            var statistics = await _experimentService.GetStatisticsAsync();
            
            TotalExperiments = statistics.TotalExperiments;
            RunningExperiments = statistics.RunningExperiments;
            CompletedExperiments = statistics.CompletedExperiments;
            FailedExperiments = statistics.FailedExperiments;

        }, "正在加载统计数据...");
    }

    #endregion

    #region 命令处理方法

    /// <summary>
    /// 创建实验
    /// </summary>
    private void CreateExperiment()
    {
        _eventPublisher.PublishInfoNotification("创建实验功能即将开放");
    }

    /// <summary>
    /// 查看实验列表
    /// </summary>
    private void ViewExperiments()
    {
        _regionManager.RequestNavigate("ContentRegion", "ExperimentListView");
    }

    /// <summary>
    /// 查看报告
    /// </summary>
    private void ViewReports()
    {
        _eventPublisher.PublishInfoNotification("报告功能即将开放");
    }

    #endregion
}

<prism:PrismApplication x:Class="LabDemo.App.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:prism="http://prismlibrary.com/">
    <!-- 
    为什么不设置StartupUri？
    因为Prism框架会通过CreateShell方法来创建和显示主窗口
    这样可以确保依赖注入容器在窗口创建之前就已经配置完成
    -->
    <prism:PrismApplication.Resources>
        <!-- 全局样式和资源 -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- 可以在这里引入主题样式 -->
            </ResourceDictionary.MergedDictionaries>
            
            <!-- 全局颜色定义 -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#FFC107"/>
            <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
            <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
            <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        </ResourceDictionary>
    </prism:PrismApplication.Resources>
</prism:PrismApplication>

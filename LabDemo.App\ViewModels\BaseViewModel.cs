using Prism.Mvvm;
using Prism.Commands;
using System.ComponentModel;

namespace LabDemo.App.ViewModels;

/// <summary>
/// 视图模型基类
/// 
/// 为什么需要基类？
/// 1. 代码复用：公共功能只需要实现一次
/// 2. 统一标准：确保所有视图模型遵循相同的模式
/// 3. 维护性：修改基类就能影响所有子类
/// 4. 扩展性：可以在基类中添加新的公共功能
/// </summary>
public abstract class BaseViewModel : BindableBase
{
    private bool _isBusy;
    private string _busyMessage = "正在处理...";
    private string _title = string.Empty;

    /// <summary>
    /// 是否正在忙碌
    /// 为什么需要忙碌状态？
    /// 1. 用户体验：显示加载指示器，告知用户操作正在进行
    /// 2. 防止重复操作：禁用按钮防止用户重复点击
    /// 3. 状态管理：统一管理异步操作的状态
    /// </summary>
    public bool IsBusy
    {
        get => _isBusy;
        set
        {
            SetProperty(ref _isBusy, value);
            // 当忙碌状态改变时，通知所有命令重新评估可执行状态
            RaiseCanExecuteChanged();
        }
    }

    /// <summary>
    /// 忙碌时显示的消息
    /// </summary>
    public string BusyMessage
    {
        get => _busyMessage;
        set => SetProperty(ref _busyMessage, value);
    }

    /// <summary>
    /// 视图标题
    /// </summary>
    public string Title
    {
        get => _title;
        set => SetProperty(ref _title, value);
    }

    /// <summary>
    /// 所有命令的集合 - 用于批量更新可执行状态
    /// </summary>
    private readonly List<DelegateCommand> _commands = new();

    /// <summary>
    /// 注册命令 - 子类调用此方法注册命令
    /// </summary>
    /// <param name="command">要注册的命令</param>
    protected void RegisterCommand(DelegateCommand command)
    {
        _commands.Add(command);
    }

    /// <summary>
    /// 通知所有命令重新评估可执行状态
    /// </summary>
    protected void RaiseCanExecuteChanged()
    {
        foreach (var command in _commands)
        {
            command.RaiseCanExecuteChanged();
        }
    }

    /// <summary>
    /// 执行异步操作的辅助方法
    /// 
    /// 为什么需要这个方法？
    /// 1. 统一处理：所有异步操作都使用相同的模式
    /// 2. 异常处理：统一的异常处理逻辑
    /// 3. 状态管理：自动管理忙碌状态
    /// 4. 用户体验：显示加载指示器
    /// </summary>
    /// <param name="asyncAction">要执行的异步操作</param>
    /// <param name="busyMessage">忙碌时显示的消息</param>
    /// <param name="onError">错误处理回调</param>
    protected async Task ExecuteAsync(Func<Task> asyncAction, 
        string busyMessage = "正在处理...", 
        Action<Exception>? onError = null)
    {
        if (IsBusy) return; // 防止重复执行

        try
        {
            IsBusy = true;
            BusyMessage = busyMessage;
            
            await asyncAction();
        }
        catch (Exception ex)
        {
            // 如果提供了错误处理回调，则调用它
            if (onError != null)
            {
                onError(ex);
            }
            else
            {
                // 默认错误处理
                HandleError(ex);
            }
        }
        finally
        {
            IsBusy = false;
        }
    }

    /// <summary>
    /// 执行异步操作并返回结果的辅助方法
    /// </summary>
    /// <typeparam name="T">返回值类型</typeparam>
    /// <param name="asyncFunc">要执行的异步函数</param>
    /// <param name="busyMessage">忙碌时显示的消息</param>
    /// <param name="onError">错误处理回调</param>
    /// <returns>操作结果</returns>
    protected async Task<T?> ExecuteAsync<T>(Func<Task<T>> asyncFunc, 
        string busyMessage = "正在处理...", 
        Func<Exception, T>? onError = null)
    {
        if (IsBusy) return default(T);

        try
        {
            IsBusy = true;
            BusyMessage = busyMessage;
            
            return await asyncFunc();
        }
        catch (Exception ex)
        {
            if (onError != null)
            {
                return onError(ex);
            }
            else
            {
                HandleError(ex);
                return default(T);
            }
        }
        finally
        {
            IsBusy = false;
        }
    }

    /// <summary>
    /// 默认错误处理方法
    /// 子类可以重写此方法来自定义错误处理逻辑
    /// </summary>
    /// <param name="exception">异常对象</param>
    protected virtual void HandleError(Exception exception)
    {
        // 这里可以集成日志框架
        System.Diagnostics.Debug.WriteLine($"错误: {exception.Message}");
        
        // 在实际应用中，这里可能会：
        // 1. 记录日志
        // 2. 显示用户友好的错误消息
        // 3. 发送错误报告
        // 4. 执行错误恢复逻辑
    }

    /// <summary>
    /// 创建可以在忙碌时禁用的命令
    /// </summary>
    /// <param name="executeMethod">执行方法</param>
    /// <param name="canExecuteMethod">可执行条件方法（可选）</param>
    /// <returns>委托命令</returns>
    protected DelegateCommand CreateCommand(Action executeMethod, Func<bool>? canExecuteMethod = null)
    {
        var command = new DelegateCommand(executeMethod, () => 
            !IsBusy && (canExecuteMethod?.Invoke() ?? true));
        
        RegisterCommand(command);
        return command;
    }

    /// <summary>
    /// 创建带参数的可以在忙碌时禁用的命令
    /// </summary>
    /// <typeparam name="T">参数类型</typeparam>
    /// <param name="executeMethod">执行方法</param>
    /// <param name="canExecuteMethod">可执行条件方法（可选）</param>
    /// <returns>委托命令</returns>
    protected DelegateCommand<T> CreateCommand<T>(Action<T> executeMethod, Func<T, bool>? canExecuteMethod = null)
    {
        var command = new DelegateCommand<T>(executeMethod, (parameter) => 
            !IsBusy && (canExecuteMethod?.Invoke(parameter) ?? true));
        
        // 注意：DelegateCommand<T> 不继承自 DelegateCommand，所以这里需要特殊处理
        // 在实际项目中，可能需要更复杂的命令管理机制
        return command;
    }
}

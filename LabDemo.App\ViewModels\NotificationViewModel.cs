using LabDemo.Service.Events;

namespace LabDemo.App.ViewModels;

/// <summary>
/// 通知视图模型
/// 
/// 这个类展示了简单视图模型的设计：
/// 1. 数据封装：封装通知相关的数据
/// 2. 计算属性：提供UI需要的计算值
/// 3. 类型转换：将业务类型转换为UI友好的类型
/// </summary>
public class NotificationViewModel : BaseViewModel
{
    private string _title = string.Empty;
    private string _message = string.Empty;
    private NotificationType _type;
    private DateTime _timestamp = DateTime.Now;
    private bool _autoClose = true;
    private int _autoCloseDelay = 5;

    /// <summary>
    /// 通知标题
    /// </summary>
    public new string Title
    {
        get => _title;
        set => SetProperty(ref _title, value);
    }

    /// <summary>
    /// 通知消息
    /// </summary>
    public string Message
    {
        get => _message;
        set => SetProperty(ref _message, value);
    }

    /// <summary>
    /// 通知类型
    /// </summary>
    public NotificationType Type
    {
        get => _type;
        set => SetProperty(ref _type, value);
    }

    /// <summary>
    /// 通知时间
    /// </summary>
    public DateTime Timestamp
    {
        get => _timestamp;
        set => SetProperty(ref _timestamp, value);
    }

    /// <summary>
    /// 是否自动关闭
    /// </summary>
    public bool AutoClose
    {
        get => _autoClose;
        set => SetProperty(ref _autoClose, value);
    }

    /// <summary>
    /// 自动关闭延迟（秒）
    /// </summary>
    public int AutoCloseDelay
    {
        get => _autoCloseDelay;
        set => SetProperty(ref _autoCloseDelay, value);
    }

    /// <summary>
    /// 计算属性：通知图标
    /// 
    /// 为什么使用计算属性？
    /// 1. UI友好：直接提供UI需要的值
    /// 2. 集中逻辑：将转换逻辑集中在一个地方
    /// 3. 自动更新：当依赖属性变化时自动更新
    /// </summary>
    public string Icon
    {
        get
        {
            return Type switch
            {
                NotificationType.Information => "ℹ️",
                NotificationType.Success => "✅",
                NotificationType.Warning => "⚠️",
                NotificationType.Error => "❌",
                _ => "ℹ️"
            };
        }
    }

    /// <summary>
    /// 计算属性：通知颜色
    /// </summary>
    public string Color
    {
        get
        {
            return Type switch
            {
                NotificationType.Information => "#2196F3", // 蓝色
                NotificationType.Success => "#4CAF50",     // 绿色
                NotificationType.Warning => "#FF9800",     // 橙色
                NotificationType.Error => "#F44336",       // 红色
                _ => "#2196F3"
            };
        }
    }

    /// <summary>
    /// 计算属性：格式化的时间戳
    /// </summary>
    public string FormattedTimestamp
    {
        get
        {
            var now = DateTime.Now;
            var diff = now - Timestamp;

            if (diff.TotalMinutes < 1)
                return "刚刚";
            else if (diff.TotalMinutes < 60)
                return $"{(int)diff.TotalMinutes}分钟前";
            else if (diff.TotalHours < 24)
                return $"{(int)diff.TotalHours}小时前";
            else
                return Timestamp.ToString("MM-dd HH:mm");
        }
    }

    /// <summary>
    /// 计算属性：通知类型的显示名称
    /// </summary>
    public string TypeDisplayName
    {
        get
        {
            return Type switch
            {
                NotificationType.Information => "信息",
                NotificationType.Success => "成功",
                NotificationType.Warning => "警告",
                NotificationType.Error => "错误",
                _ => "未知"
            };
        }
    }
}

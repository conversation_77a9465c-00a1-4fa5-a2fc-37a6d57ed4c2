using System.ComponentModel.DataAnnotations;
using LabDemo.Domain.Enums;

namespace LabDemo.Domain.Entities;

/// <summary>
/// 实验实体类 - 领域模型的核心
/// 为什么要有实体类？
/// 1. 表示业务领域中的核心概念
/// 2. 封装业务规则和验证逻辑
/// 3. 提供类型安全的数据操作
/// </summary>
public class Experiment : BaseEntity
{
    /// <summary>
    /// 实验名称 - 必填字段，最大长度100
    /// </summary>
    [Required(ErrorMessage = "实验名称不能为空")]
    [MaxLength(100, ErrorMessage = "实验名称不能超过100个字符")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 实验描述 - 可选字段，详细说明实验内容
    /// </summary>
    [MaxLength(500, ErrorMessage = "实验描述不能超过500个字符")]
    public string? Description { get; set; }

    /// <summary>
    /// 实验类型 - 使用枚举确保数据一致性
    /// </summary>
    public ExperimentType Type { get; set; }

    /// <summary>
    /// 实验状态 - 使用枚举管理实验生命周期
    /// </summary>
    public ExperimentStatus Status { get; set; } = ExperimentStatus.Created;

    /// <summary>
    /// 开始时间 - 实验实际开始执行的时间
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 结束时间 - 实验完成的时间
    /// </summary>
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 实验结果 - 存储实验的输出结果
    /// </summary>
    public string? Result { get; set; }

    /// <summary>
    /// 优先级 - 用于任务调度排序
    /// </summary>
    public int Priority { get; set; } = 1;

    /// <summary>
    /// 实验步骤集合 - 一对多关系
    /// 为什么用导航属性？
    /// 1. 表达实体间的关系
    /// 2. 支持延迟加载
    /// 3. 简化关联数据查询
    /// </summary>
    public virtual ICollection<ExperimentStep> Steps { get; set; } = new List<ExperimentStep>();

    /// <summary>
    /// 业务方法：开始实验
    /// 为什么在实体中放业务方法？
    /// 1. 封装业务规则
    /// 2. 确保数据一致性
    /// 3. 提高代码可读性
    /// </summary>
    public void Start()
    {
        if (Status != ExperimentStatus.Created)
            throw new InvalidOperationException($"只有状态为'已创建'的实验才能开始，当前状态：{Status}");

        Status = ExperimentStatus.Running;
        StartTime = DateTime.Now;
    }

    /// <summary>
    /// 业务方法：完成实验
    /// </summary>
    public void Complete(string result)
    {
        if (Status != ExperimentStatus.Running)
            throw new InvalidOperationException($"只有状态为'运行中'的实验才能完成，当前状态：{Status}");

        Status = ExperimentStatus.Completed;
        EndTime = DateTime.Now;
        Result = result;
    }

    /// <summary>
    /// 业务方法：暂停实验
    /// </summary>
    public void Pause()
    {
        if (Status != ExperimentStatus.Running)
            throw new InvalidOperationException($"只有状态为'运行中'的实验才能暂停，当前状态：{Status}");

        Status = ExperimentStatus.Paused;
    }

    /// <summary>
    /// 业务方法：恢复实验
    /// </summary>
    public void Resume()
    {
        if (Status != ExperimentStatus.Paused)
            throw new InvalidOperationException($"只有状态为'已暂停'的实验才能恢复，当前状态：{Status}");

        Status = ExperimentStatus.Running;
    }

    /// <summary>
    /// 计算属性：实验持续时间
    /// 为什么用计算属性？
    /// 1. 避免数据冗余
    /// 2. 确保数据实时性
    /// 3. 简化业务逻辑
    /// </summary>
    public TimeSpan? Duration
    {
        get
        {
            if (StartTime == null) return null;
            var endTime = EndTime ?? DateTime.Now;
            return endTime - StartTime.Value;
        }
    }
}

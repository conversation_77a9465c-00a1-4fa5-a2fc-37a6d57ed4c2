using System.Linq.Expressions;
using LabDemo.Domain.Entities;
using LabDemo.Domain.Enums;

namespace LabDemo.Service.Interfaces;

/// <summary>
/// 实验服务接口
/// 为什么需要服务接口？
/// 1. 依赖倒置：高层模块不依赖低层模块，都依赖抽象
/// 2. 可测试性：可以轻松创建Mock对象进行单元测试
/// 3. 可替换性：可以有多种实现（内存、数据库、Web API等）
/// 4. 契约定义：明确定义服务的能力边界
/// </summary>
public interface IExperimentService
{
    /// <summary>
    /// 创建新实验
    /// </summary>
    /// <param name="experiment">实验对象</param>
    /// <returns>创建的实验</returns>
    Task<Experiment> CreateExperimentAsync(Experiment experiment);

    /// <summary>
    /// 根据ID获取实验
    /// </summary>
    /// <param name="id">实验ID</param>
    /// <returns>实验对象，如果不存在返回null</returns>
    Task<Experiment?> GetExperimentByIdAsync(string id);

    /// <summary>
    /// 获取所有实验
    /// </summary>
    /// <returns>实验列表</returns>
    Task<IEnumerable<Experiment>> GetAllExperimentsAsync();

    /// <summary>
    /// 根据条件查询实验 - 使用表达式树实现动态查询
    /// 为什么使用表达式树？
    /// 1. 类型安全：编译时检查，避免字符串拼接SQL的错误
    /// 2. 智能提示：IDE可以提供完整的智能提示
    /// 3. 重构友好：重命名属性时会自动更新查询条件
    /// 4. 性能优化：可以转换为高效的SQL查询
    /// </summary>
    /// <param name="predicate">查询条件表达式</param>
    /// <returns>符合条件的实验列表</returns>
    Task<IEnumerable<Experiment>> GetExperimentsByConditionAsync(Expression<Func<Experiment, bool>> predicate);

    /// <summary>
    /// 根据状态获取实验
    /// </summary>
    /// <param name="status">实验状态</param>
    /// <returns>指定状态的实验列表</returns>
    Task<IEnumerable<Experiment>> GetExperimentsByStatusAsync(ExperimentStatus status);

    /// <summary>
    /// 根据类型获取实验
    /// </summary>
    /// <param name="type">实验类型</param>
    /// <returns>指定类型的实验列表</returns>
    Task<IEnumerable<Experiment>> GetExperimentsByTypeAsync(ExperimentType type);

    /// <summary>
    /// 更新实验
    /// </summary>
    /// <param name="experiment">要更新的实验</param>
    /// <returns>更新后的实验</returns>
    Task<Experiment> UpdateExperimentAsync(Experiment experiment);

    /// <summary>
    /// 删除实验（软删除）
    /// </summary>
    /// <param name="id">实验ID</param>
    /// <returns>是否删除成功</returns>
    Task<bool> DeleteExperimentAsync(string id);

    /// <summary>
    /// 开始实验
    /// </summary>
    /// <param name="id">实验ID</param>
    /// <returns>是否开始成功</returns>
    Task<bool> StartExperimentAsync(string id);

    /// <summary>
    /// 暂停实验
    /// </summary>
    /// <param name="id">实验ID</param>
    /// <returns>是否暂停成功</returns>
    Task<bool> PauseExperimentAsync(string id);

    /// <summary>
    /// 恢复实验
    /// </summary>
    /// <param name="id">实验ID</param>
    /// <returns>是否恢复成功</returns>
    Task<bool> ResumeExperimentAsync(string id);

    /// <summary>
    /// 完成实验
    /// </summary>
    /// <param name="id">实验ID</param>
    /// <param name="result">实验结果</param>
    /// <returns>是否完成成功</returns>
    Task<bool> CompleteExperimentAsync(string id, string result);

    /// <summary>
    /// 获取实验统计信息
    /// 为什么需要统计方法？
    /// 1. 业务洞察：了解实验执行情况
    /// 2. 性能监控：识别瓶颈和问题
    /// 3. 报告生成：为管理层提供数据支持
    /// </summary>
    /// <returns>统计信息</returns>
    Task<ExperimentStatistics> GetStatisticsAsync();
}

/// <summary>
/// 实验统计信息
/// </summary>
public class ExperimentStatistics
{
    /// <summary>
    /// 总实验数
    /// </summary>
    public int TotalExperiments { get; set; }

    /// <summary>
    /// 运行中的实验数
    /// </summary>
    public int RunningExperiments { get; set; }

    /// <summary>
    /// 已完成的实验数
    /// </summary>
    public int CompletedExperiments { get; set; }

    /// <summary>
    /// 失败的实验数
    /// </summary>
    public int FailedExperiments { get; set; }

    /// <summary>
    /// 平均执行时间（分钟）
    /// </summary>
    public double AverageExecutionTimeMinutes { get; set; }

    /// <summary>
    /// 按类型分组的统计
    /// </summary>
    public Dictionary<ExperimentType, int> ExperimentsByType { get; set; } = new();

    /// <summary>
    /// 按状态分组的统计
    /// </summary>
    public Dictionary<ExperimentStatus, int> ExperimentsByStatus { get; set; } = new();
}

namespace LabDemo.Service.Configuration;

/// <summary>
/// 配置服务接口
/// 
/// 为什么需要配置服务？
/// 1. 集中管理：统一管理应用程序的所有配置
/// 2. 环境隔离：支持不同环境的配置（开发、测试、生产）
/// 3. 动态更新：支持运行时配置更新
/// 4. 类型安全：提供强类型的配置访问
/// 5. 默认值：提供合理的默认配置值
/// </summary>
public interface IConfigurationService
{
    /// <summary>
    /// 获取字符串配置值
    /// </summary>
    /// <param name="key">配置键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>配置值</returns>
    string GetString(string key, string defaultValue = "");

    /// <summary>
    /// 获取整数配置值
    /// </summary>
    /// <param name="key">配置键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>配置值</returns>
    int GetInt(string key, int defaultValue = 0);

    /// <summary>
    /// 获取布尔配置值
    /// </summary>
    /// <param name="key">配置键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>配置值</returns>
    bool GetBool(string key, bool defaultValue = false);

    /// <summary>
    /// 获取双精度浮点配置值
    /// </summary>
    /// <param name="key">配置键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>配置值</returns>
    double GetDouble(string key, double defaultValue = 0.0);

    /// <summary>
    /// 获取强类型配置对象
    /// </summary>
    /// <typeparam name="T">配置对象类型</typeparam>
    /// <param name="sectionKey">配置节键</param>
    /// <returns>配置对象</returns>
    T GetSection<T>(string sectionKey) where T : class, new();

    /// <summary>
    /// 设置配置值
    /// </summary>
    /// <param name="key">配置键</param>
    /// <param name="value">配置值</param>
    void SetValue(string key, object value);

    /// <summary>
    /// 保存配置到文件
    /// </summary>
    Task SaveAsync();

    /// <summary>
    /// 重新加载配置
    /// </summary>
    Task ReloadAsync();

    /// <summary>
    /// 配置变更事件
    /// </summary>
    event EventHandler<ConfigurationChangedEventArgs> ConfigurationChanged;
}

/// <summary>
/// 配置变更事件参数
/// </summary>
public class ConfigurationChangedEventArgs : EventArgs
{
    /// <summary>
    /// 变更的配置键
    /// </summary>
    public string Key { get; set; } = string.Empty;

    /// <summary>
    /// 旧值
    /// </summary>
    public object? OldValue { get; set; }

    /// <summary>
    /// 新值
    /// </summary>
    public object? NewValue { get; set; }

    /// <summary>
    /// 变更时间
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.Now;
}

/// <summary>
/// 应用程序配置
/// 
/// 这个类展示了如何定义强类型的配置对象
/// 使用属性和默认值来确保配置的完整性
/// </summary>
public class ApplicationConfiguration
{
    /// <summary>
    /// 应用程序名称
    /// </summary>
    public string ApplicationName { get; set; } = "实验室管理系统";

    /// <summary>
    /// 应用程序版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 数据库配置
    /// </summary>
    public DatabaseConfiguration Database { get; set; } = new();

    /// <summary>
    /// 日志配置
    /// </summary>
    public LoggingConfiguration Logging { get; set; } = new();

    /// <summary>
    /// UI配置
    /// </summary>
    public UIConfiguration UI { get; set; } = new();

    /// <summary>
    /// 实验配置
    /// </summary>
    public ExperimentConfiguration Experiment { get; set; } = new();
}

/// <summary>
/// 数据库配置
/// </summary>
public class DatabaseConfiguration
{
    /// <summary>
    /// 连接字符串
    /// </summary>
    public string ConnectionString { get; set; } = "Data Source=lab.db";

    /// <summary>
    /// 连接超时时间（秒）
    /// </summary>
    public int ConnectionTimeout { get; set; } = 30;

    /// <summary>
    /// 命令超时时间（秒）
    /// </summary>
    public int CommandTimeout { get; set; } = 60;

    /// <summary>
    /// 是否启用连接池
    /// </summary>
    public bool EnableConnectionPooling { get; set; } = true;

    /// <summary>
    /// 最大连接数
    /// </summary>
    public int MaxConnections { get; set; } = 100;
}

/// <summary>
/// 日志配置
/// </summary>
public class LoggingConfiguration
{
    /// <summary>
    /// 日志级别
    /// </summary>
    public string LogLevel { get; set; } = "Information";

    /// <summary>
    /// 日志文件路径
    /// </summary>
    public string LogFilePath { get; set; } = "logs/app.log";

    /// <summary>
    /// 是否启用控制台日志
    /// </summary>
    public bool EnableConsoleLogging { get; set; } = true;

    /// <summary>
    /// 是否启用文件日志
    /// </summary>
    public bool EnableFileLogging { get; set; } = true;

    /// <summary>
    /// 日志文件最大大小（MB）
    /// </summary>
    public int MaxFileSizeMB { get; set; } = 10;

    /// <summary>
    /// 保留的日志文件数量
    /// </summary>
    public int RetainedFileCount { get; set; } = 7;
}

/// <summary>
/// UI配置
/// </summary>
public class UIConfiguration
{
    /// <summary>
    /// 主题名称
    /// </summary>
    public string Theme { get; set; } = "Light";

    /// <summary>
    /// 语言
    /// </summary>
    public string Language { get; set; } = "zh-CN";

    /// <summary>
    /// 是否启用动画
    /// </summary>
    public bool EnableAnimations { get; set; } = true;

    /// <summary>
    /// 自动保存间隔（分钟）
    /// </summary>
    public int AutoSaveIntervalMinutes { get; set; } = 5;

    /// <summary>
    /// 通知自动关闭延迟（秒）
    /// </summary>
    public int NotificationAutoCloseDelay { get; set; } = 5;

    /// <summary>
    /// 最大通知数量
    /// </summary>
    public int MaxNotificationCount { get; set; } = 10;
}

/// <summary>
/// 实验配置
/// </summary>
public class ExperimentConfiguration
{
    /// <summary>
    /// 默认实验超时时间（小时）
    /// </summary>
    public int DefaultTimeoutHours { get; set; } = 24;

    /// <summary>
    /// 最大并发实验数
    /// </summary>
    public int MaxConcurrentExperiments { get; set; } = 5;

    /// <summary>
    /// 是否启用自动备份
    /// </summary>
    public bool EnableAutoBackup { get; set; } = true;

    /// <summary>
    /// 备份间隔（小时）
    /// </summary>
    public int BackupIntervalHours { get; set; } = 6;

    /// <summary>
    /// 实验数据保留天数
    /// </summary>
    public int DataRetentionDays { get; set; } = 365;

    /// <summary>
    /// 是否启用实验步骤验证
    /// </summary>
    public bool EnableStepValidation { get; set; } = true;

    /// <summary>
    /// 默认优先级
    /// </summary>
    public int DefaultPriority { get; set; } = 1;
}

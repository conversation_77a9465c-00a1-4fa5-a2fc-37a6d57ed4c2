{"format": 1, "restore": {"D:\\HoursCode\\GC4bug\\src\\LabDemo.Service\\LabDemo.Service.csproj": {}}, "projects": {"D:\\HoursCode\\GC4bug\\src\\LabDemo.Domain\\LabDemo.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\HoursCode\\GC4bug\\src\\LabDemo.Domain\\LabDemo.Domain.csproj", "projectName": "LabDemo.Domain", "projectPath": "D:\\HoursCode\\GC4bug\\src\\LabDemo.Domain\\LabDemo.Domain.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\HoursCode\\GC4bug\\src\\LabDemo.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}, "D:\\HoursCode\\GC4bug\\src\\LabDemo.Service\\LabDemo.Service.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\HoursCode\\GC4bug\\src\\LabDemo.Service\\LabDemo.Service.csproj", "projectName": "LabDemo.Service", "projectPath": "D:\\HoursCode\\GC4bug\\src\\LabDemo.Service\\LabDemo.Service.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\HoursCode\\GC4bug\\src\\LabDemo.Service\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\HoursCode\\GC4bug\\src\\LabDemo.Domain\\LabDemo.Domain.csproj": {"projectPath": "D:\\HoursCode\\GC4bug\\src\\LabDemo.Domain\\LabDemo.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Prism.Core": {"target": "Package", "version": "[8.1.97, )"}, "System.Linq.Expressions": {"target": "Package", "version": "[4.3.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304\\RuntimeIdentifierGraph.json"}}}}}
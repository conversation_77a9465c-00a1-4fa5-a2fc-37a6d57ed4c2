using System.Windows;

namespace LabDemo.App.Views;

/// <summary>
/// MainWindow.xaml 的交互逻辑
/// 
/// 在MVVM模式中，主窗口的代码后置文件应该尽可能简单
/// 主要职责：
/// 1. 初始化UI组件
/// 2. 处理纯UI相关的事件（如窗口生命周期事件）
/// 3. 实现特定的UI行为（如拖拽、动画等）
/// 
/// 业务逻辑应该全部放在ViewModel中
/// </summary>
public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();
        
        // 设置窗口图标（如果有的话）
        // this.Icon = new BitmapImage(new Uri("pack://application:,,,/Resources/app-icon.ico"));
        
        // 窗口加载完成后的处理
        this.Loaded += OnWindowLoaded;
        
        // 窗口关闭时的处理
        this.Closing += OnWindowClosing;
    }

    /// <summary>
    /// 窗口加载完成事件处理
    /// </summary>
    private void OnWindowLoaded(object sender, RoutedEventArgs e)
    {
        // 这里可以执行一些UI初始化工作
        // 例如：设置焦点、启动动画等
        
        // 在实际应用中，可能需要检查用户权限、加载用户设置等
        // 但这些业务逻辑应该通过ViewModel来处理
    }

    /// <summary>
    /// 窗口关闭事件处理
    /// </summary>
    private void OnWindowClosing(object sender, System.ComponentModel.CancelEventArgs e)
    {
        // 这里可以执行一些清理工作
        // 例如：保存窗口位置、确认是否保存未保存的数据等
        
        // 如果需要阻止窗口关闭，可以设置 e.Cancel = true
        // 但通常这种逻辑应该在ViewModel中处理
        
        // 示例：确认关闭对话框
        // var result = MessageBox.Show("确定要退出应用程序吗？", "确认", 
        //     MessageBoxButton.YesNo, MessageBoxImage.Question);
        // if (result == MessageBoxResult.No)
        // {
        //     e.Cancel = true;
        // }
    }
}

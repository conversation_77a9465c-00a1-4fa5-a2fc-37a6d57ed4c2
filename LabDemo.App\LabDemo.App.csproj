<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWPF>true</UseWPF>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\LabDemo.Domain\LabDemo.Domain.csproj" />
    <ProjectReference Include="..\LabDemo.Service\LabDemo.Service.csproj" />
    <ProjectReference Include="..\LabDemo.ExperimentModule\LabDemo.ExperimentModule.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Prism.Wpf" Version="8.1.97" />
    <PackageReference Include="Prism.DryIoc" Version="8.1.97" />
  </ItemGroup>

</Project>

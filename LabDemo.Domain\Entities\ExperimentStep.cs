using System.ComponentModel.DataAnnotations;
using LabDemo.Domain.Enums;

namespace LabDemo.Domain.Entities;

/// <summary>
/// 实验步骤实体类
/// 为什么需要步骤实体？
/// 1. 将复杂的实验分解为可管理的步骤
/// 2. 支持步骤级别的状态跟踪
/// 3. 便于实现工作流引擎
/// </summary>
public class ExperimentStep : BaseEntity
{
    /// <summary>
    /// 步骤名称
    /// </summary>
    [Required(ErrorMessage = "步骤名称不能为空")]
    [MaxLength(100, ErrorMessage = "步骤名称不能超过100个字符")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 步骤描述
    /// </summary>
    [MaxLength(500, ErrorMessage = "步骤描述不能超过500个字符")]
    public string? Description { get; set; }

    /// <summary>
    /// 步骤类型 - 决定使用哪种执行策略
    /// </summary>
    public StepType Type { get; set; }

    /// <summary>
    /// 步骤状态
    /// </summary>
    public StepStatus Status { get; set; } = StepStatus.Pending;

    /// <summary>
    /// 执行顺序 - 用于步骤排序
    /// </summary>
    public int Order { get; set; }

    /// <summary>
    /// 预计执行时间（分钟）
    /// </summary>
    public int EstimatedDurationMinutes { get; set; }

    /// <summary>
    /// 实际开始时间
    /// </summary>
    public DateTime? ActualStartTime { get; set; }

    /// <summary>
    /// 实际结束时间
    /// </summary>
    public DateTime? ActualEndTime { get; set; }

    /// <summary>
    /// 步骤参数 - JSON格式存储步骤特定的参数
    /// 为什么用JSON？
    /// 1. 灵活性：不同类型步骤有不同参数
    /// 2. 扩展性：新增参数不需要修改数据库结构
    /// 3. 简单性：避免复杂的关联表设计
    /// </summary>
    public string? Parameters { get; set; }

    /// <summary>
    /// 步骤结果
    /// </summary>
    public string? Result { get; set; }

    /// <summary>
    /// 错误信息 - 步骤执行失败时的错误详情
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 所属实验ID - 外键
    /// </summary>
    [Required]
    public string ExperimentId { get; set; } = string.Empty;

    /// <summary>
    /// 所属实验 - 导航属性
    /// </summary>
    public virtual Experiment? Experiment { get; set; }

    /// <summary>
    /// 业务方法：开始执行步骤
    /// </summary>
    public void Start()
    {
        if (Status != StepStatus.Pending)
            throw new InvalidOperationException($"只有状态为'待执行'的步骤才能开始，当前状态：{Status}");

        Status = StepStatus.Running;
        ActualStartTime = DateTime.Now;
    }

    /// <summary>
    /// 业务方法：完成步骤
    /// </summary>
    public void Complete(string? result = null)
    {
        if (Status != StepStatus.Running)
            throw new InvalidOperationException($"只有状态为'执行中'的步骤才能完成，当前状态：{Status}");

        Status = StepStatus.Completed;
        ActualEndTime = DateTime.Now;
        Result = result;
    }

    /// <summary>
    /// 业务方法：步骤执行失败
    /// </summary>
    public void Fail(string errorMessage)
    {
        if (Status != StepStatus.Running)
            throw new InvalidOperationException($"只有状态为'执行中'的步骤才能标记为失败，当前状态：{Status}");

        Status = StepStatus.Failed;
        ActualEndTime = DateTime.Now;
        ErrorMessage = errorMessage;
    }

    /// <summary>
    /// 计算属性：实际执行时间
    /// </summary>
    public TimeSpan? ActualDuration
    {
        get
        {
            if (ActualStartTime == null) return null;
            var endTime = ActualEndTime ?? DateTime.Now;
            return endTime - ActualStartTime.Value;
        }
    }

    /// <summary>
    /// 计算属性：是否超时
    /// </summary>
    public bool IsOverdue
    {
        get
        {
            if (ActualStartTime == null || Status == StepStatus.Completed) return false;
            var elapsed = DateTime.Now - ActualStartTime.Value;
            return elapsed.TotalMinutes > EstimatedDurationMinutes;
        }
    }
}

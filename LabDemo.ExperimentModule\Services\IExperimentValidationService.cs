using LabDemo.Domain.Entities;

namespace LabDemo.ExperimentModule.Services;

/// <summary>
/// 实验验证服务接口
/// 
/// 为什么需要验证服务？
/// 1. 数据完整性：确保实验数据的完整性和正确性
/// 2. 业务规则：实施复杂的业务验证规则
/// 3. 安全性：防止无效或恶意数据进入系统
/// 4. 用户体验：提供即时的验证反馈
/// </summary>
public interface IExperimentValidationService
{
    /// <summary>
    /// 加载验证规则
    /// </summary>
    void LoadValidationRules();

    /// <summary>
    /// 验证实验数据
    /// </summary>
    /// <param name="experiment">实验对象</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateExperimentAsync(Experiment experiment);

    /// <summary>
    /// 验证实验步骤
    /// </summary>
    /// <param name="step">实验步骤</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateExperimentStepAsync(ExperimentStep step);

    /// <summary>
    /// 验证实验是否可以开始
    /// </summary>
    /// <param name="experimentId">实验ID</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateExperimentCanStartAsync(string experimentId);

    /// <summary>
    /// 验证实验是否可以暂停
    /// </summary>
    /// <param name="experimentId">实验ID</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateExperimentCanPauseAsync(string experimentId);

    /// <summary>
    /// 验证实验是否可以完成
    /// </summary>
    /// <param name="experimentId">实验ID</param>
    /// <returns>验证结果</returns>
    Task<ValidationResult> ValidateExperimentCanCompleteAsync(string experimentId);

    /// <summary>
    /// 获取验证规则列表
    /// </summary>
    /// <returns>验证规则列表</returns>
    IEnumerable<ValidationRule> GetValidationRules();
}

/// <summary>
/// 验证规则
/// </summary>
public class ValidationRule
{
    /// <summary>
    /// 规则ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 规则名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 规则描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 规则类型
    /// </summary>
    public ValidationRuleType Type { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 错误级别
    /// </summary>
    public ValidationLevel Level { get; set; } = ValidationLevel.Error;

    /// <summary>
    /// 错误消息模板
    /// </summary>
    public string ErrorMessageTemplate { get; set; } = string.Empty;

    /// <summary>
    /// 规则参数
    /// </summary>
    public Dictionary<string, object> Parameters { get; set; } = new();
}

/// <summary>
/// 验证规则类型
/// </summary>
public enum ValidationRuleType
{
    /// <summary>
    /// 必填验证
    /// </summary>
    Required,

    /// <summary>
    /// 长度验证
    /// </summary>
    Length,

    /// <summary>
    /// 范围验证
    /// </summary>
    Range,

    /// <summary>
    /// 格式验证
    /// </summary>
    Format,

    /// <summary>
    /// 自定义验证
    /// </summary>
    Custom,

    /// <summary>
    /// 业务规则验证
    /// </summary>
    BusinessRule
}

/// <summary>
/// 验证级别
/// </summary>
public enum ValidationLevel
{
    /// <summary>
    /// 信息
    /// </summary>
    Information,

    /// <summary>
    /// 警告
    /// </summary>
    Warning,

    /// <summary>
    /// 错误
    /// </summary>
    Error,

    /// <summary>
    /// 严重错误
    /// </summary>
    Critical
}

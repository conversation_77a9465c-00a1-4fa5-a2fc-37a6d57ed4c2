using System.Collections.ObjectModel;
using System.Linq.Expressions;
using System.Windows;
using Prism.Commands;
using Prism.Events;
using LabDemo.Domain.Entities;
using LabDemo.Domain.Enums;
using LabDemo.Service.Interfaces;
using LabDemo.Service.Events;

namespace LabDemo.App.ViewModels;

/// <summary>
/// 实验列表视图模型
/// 
/// 这个类展示了复杂视图模型的设计：
/// 1. 数据管理：管理实验列表数据
/// 2. 筛选和搜索：提供数据筛选功能
/// 3. 命令处理：处理用户操作命令
/// 4. 事件响应：响应业务事件更新UI
/// 5. 表达式树：使用表达式树实现动态查询
/// </summary>
public class ExperimentListViewModel : BaseViewModel
{
    private readonly IExperimentService _experimentService;
    private readonly IEventAggregator _eventAggregator;
    private readonly IEventPublisher _eventPublisher;

    public ExperimentListViewModel(
        IExperimentService experimentService,
        IEventAggregator eventAggregator,
        IEventPublisher eventPublisher)
    {
        _experimentService = experimentService;
        _eventAggregator = eventAggregator;
        _eventPublisher = eventPublisher;

        Title = "实验列表";

        // 初始化集合
        Experiments = new ObservableCollection<Experiment>();
        FilteredExperiments = new ObservableCollection<Experiment>();

        // 初始化命令
        InitializeCommands();

        // 订阅事件
        SubscribeToEvents();

        // 加载数据
        _ = LoadExperimentsAsync();
    }

    #region 属性

    private ObservableCollection<Experiment> _experiments = new();
    /// <summary>
    /// 所有实验列表
    /// </summary>
    public ObservableCollection<Experiment> Experiments
    {
        get => _experiments;
        set => SetProperty(ref _experiments, value);
    }

    private ObservableCollection<Experiment> _filteredExperiments = new();
    /// <summary>
    /// 筛选后的实验列表
    /// 这个集合绑定到UI，显示筛选结果
    /// </summary>
    public ObservableCollection<Experiment> FilteredExperiments
    {
        get => _filteredExperiments;
        set => SetProperty(ref _filteredExperiments, value);
    }

    private string _searchText = string.Empty;
    /// <summary>
    /// 搜索文本
    /// </summary>
    public string SearchText
    {
        get => _searchText;
        set
        {
            if (SetProperty(ref _searchText, value))
            {
                // 当搜索文本改变时，自动执行筛选
                _ = ApplyFiltersAsync();
            }
        }
    }

    private ExperimentType? _selectedType;
    /// <summary>
    /// 选中的实验类型筛选条件
    /// </summary>
    public ExperimentType? SelectedType
    {
        get => _selectedType;
        set
        {
            if (SetProperty(ref _selectedType, value))
            {
                _ = ApplyFiltersAsync();
            }
        }
    }

    private ExperimentStatus? _selectedStatus;
    /// <summary>
    /// 选中的实验状态筛选条件
    /// </summary>
    public ExperimentStatus? SelectedStatus
    {
        get => _selectedStatus;
        set
        {
            if (SetProperty(ref _selectedStatus, value))
            {
                _ = ApplyFiltersAsync();
            }
        }
    }

    private Experiment? _selectedExperiment;
    /// <summary>
    /// 当前选中的实验
    /// </summary>
    public Experiment? SelectedExperiment
    {
        get => _selectedExperiment;
        set => SetProperty(ref _selectedExperiment, value);
    }

    private bool _showOnlyMyExperiments;
    /// <summary>
    /// 是否只显示我的实验
    /// </summary>
    public bool ShowOnlyMyExperiments
    {
        get => _showOnlyMyExperiments;
        set
        {
            if (SetProperty(ref _showOnlyMyExperiments, value))
            {
                _ = ApplyFiltersAsync();
            }
        }
    }

    #endregion

    #region 命令

    /// <summary>
    /// 刷新命令
    /// </summary>
    public DelegateCommand RefreshCommand { get; private set; } = null!;

    /// <summary>
    /// 创建实验命令
    /// </summary>
    public DelegateCommand CreateExperimentCommand { get; private set; } = null!;

    /// <summary>
    /// 查看实验详情命令
    /// </summary>
    public DelegateCommand<Experiment> ViewExperimentCommand { get; private set; } = null!;

    /// <summary>
    /// 编辑实验命令
    /// </summary>
    public DelegateCommand<Experiment> EditExperimentCommand { get; private set; } = null!;

    /// <summary>
    /// 删除实验命令
    /// </summary>
    public DelegateCommand<Experiment> DeleteExperimentCommand { get; private set; } = null!;

    /// <summary>
    /// 开始实验命令
    /// </summary>
    public DelegateCommand<Experiment> StartExperimentCommand { get; private set; } = null!;

    /// <summary>
    /// 暂停实验命令
    /// </summary>
    public DelegateCommand<Experiment> PauseExperimentCommand { get; private set; } = null!;

    /// <summary>
    /// 清除筛选命令
    /// </summary>
    public DelegateCommand ClearFiltersCommand { get; private set; } = null!;

    #endregion

    #region 私有方法

    /// <summary>
    /// 初始化命令
    /// </summary>
    private void InitializeCommands()
    {
        RefreshCommand = CreateCommand(async () => await LoadExperimentsAsync());
        CreateExperimentCommand = CreateCommand(CreateExperiment);
        ClearFiltersCommand = CreateCommand(ClearFilters);

        // 带参数的命令
        ViewExperimentCommand = new DelegateCommand<Experiment>(ViewExperiment, CanViewExperiment);
        EditExperimentCommand = new DelegateCommand<Experiment>(EditExperiment, CanEditExperiment);
        DeleteExperimentCommand = new DelegateCommand<Experiment>(DeleteExperiment, CanDeleteExperiment);
        StartExperimentCommand = new DelegateCommand<Experiment>(StartExperiment, CanStartExperiment);
        PauseExperimentCommand = new DelegateCommand<Experiment>(PauseExperiment, CanPauseExperiment);
    }

    /// <summary>
    /// 订阅事件
    /// </summary>
    private void SubscribeToEvents()
    {
        // 订阅实验相关事件
        _eventAggregator.GetEvent<ExperimentCreatedEvent>()
            .Subscribe(OnExperimentCreated, ThreadOption.UIThread);

        _eventAggregator.GetEvent<ExperimentStatusChangedEvent>()
            .Subscribe(OnExperimentStatusChanged, ThreadOption.UIThread);

        _eventAggregator.GetEvent<ExperimentDeletedEvent>()
            .Subscribe(OnExperimentDeleted, ThreadOption.UIThread);
    }

    /// <summary>
    /// 加载实验列表
    /// </summary>
    private async Task LoadExperimentsAsync()
    {
        await ExecuteAsync(async () =>
        {
            var experiments = await _experimentService.GetAllExperimentsAsync();
            
            Experiments.Clear();
            foreach (var experiment in experiments)
            {
                Experiments.Add(experiment);
            }

            await ApplyFiltersAsync();

        }, "正在加载实验列表...");
    }

    /// <summary>
    /// 应用筛选条件 - 展示表达式树的高级用法
    /// 
    /// 这个方法展示了如何动态构建查询条件：
    /// 1. 根据用户输入构建表达式树
    /// 2. 组合多个筛选条件
    /// 3. 执行复杂的数据查询
    /// </summary>
    private async Task ApplyFiltersAsync()
    {
        await ExecuteAsync(async () =>
        {
            // 构建基础查询表达式
            Expression<Func<Experiment, bool>> baseFilter = e => true;

            // 动态添加筛选条件
            var filters = new List<Expression<Func<Experiment, bool>>>();

            // 文本搜索筛选
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                var searchText = SearchText.ToLower();
                filters.Add(e => e.Name.ToLower().Contains(searchText) || 
                                (e.Description != null && e.Description.ToLower().Contains(searchText)));
            }

            // 类型筛选
            if (SelectedType.HasValue)
            {
                var selectedType = SelectedType.Value;
                filters.Add(e => e.Type == selectedType);
            }

            // 状态筛选
            if (SelectedStatus.HasValue)
            {
                var selectedStatus = SelectedStatus.Value;
                filters.Add(e => e.Status == selectedStatus);
            }

            // 我的实验筛选
            if (ShowOnlyMyExperiments)
            {
                var currentUser = "管理员"; // 这里应该从用户服务获取当前用户
                filters.Add(e => e.CreatedBy == currentUser);
            }

            // 组合所有筛选条件
            var combinedFilter = CombineFilters(filters);

            // 执行筛选
            IEnumerable<Experiment> filteredResults;
            if (combinedFilter != null)
            {
                filteredResults = await _experimentService.GetExperimentsByConditionAsync(combinedFilter);
            }
            else
            {
                filteredResults = Experiments;
            }

            // 更新筛选结果
            FilteredExperiments.Clear();
            foreach (var experiment in filteredResults.OrderByDescending(e => e.CreatedAt))
            {
                FilteredExperiments.Add(experiment);
            }

        }, "正在筛选实验...");
    }

    /// <summary>
    /// 组合多个筛选条件 - 表达式树的高级应用
    /// 
    /// 这个方法展示了如何动态组合表达式树：
    /// 1. 使用Expression.AndAlso组合条件
    /// 2. 处理参数表达式的替换
    /// 3. 构建复杂的查询表达式
    /// </summary>
    private Expression<Func<Experiment, bool>>? CombineFilters(List<Expression<Func<Experiment, bool>>> filters)
    {
        if (!filters.Any()) return null;

        if (filters.Count == 1) return filters[0];

        // 组合多个表达式
        var combined = filters[0];
        for (int i = 1; i < filters.Count; i++)
        {
            combined = CombineExpressions(combined, filters[i]);
        }

        return combined;
    }

    /// <summary>
    /// 组合两个表达式
    /// </summary>
    private Expression<Func<Experiment, bool>> CombineExpressions(
        Expression<Func<Experiment, bool>> expr1,
        Expression<Func<Experiment, bool>> expr2)
    {
        // 获取参数
        var parameter = expr1.Parameters[0];

        // 替换第二个表达式的参数
        var visitor = new ParameterReplacer(expr2.Parameters[0], parameter);
        var body2 = visitor.Visit(expr2.Body);

        // 组合表达式体
        var combinedBody = Expression.AndAlso(expr1.Body, body2);

        // 创建新的Lambda表达式
        return Expression.Lambda<Func<Experiment, bool>>(combinedBody, parameter);
    }

    #endregion

    #region 命令处理方法

    /// <summary>
    /// 创建实验
    /// </summary>
    private void CreateExperiment()
    {
        // 这里可以打开创建实验的对话框
        _eventPublisher.PublishInfoNotification("创建实验功能即将开放");
    }

    /// <summary>
    /// 查看实验详情
    /// </summary>
    private void ViewExperiment(Experiment? experiment)
    {
        if (experiment == null) return;
        
        // 导航到实验详情页面
        _eventPublisher.PublishInfoNotification($"查看实验：{experiment.Name}");
    }

    /// <summary>
    /// 编辑实验
    /// </summary>
    private void EditExperiment(Experiment? experiment)
    {
        if (experiment == null) return;
        
        _eventPublisher.PublishInfoNotification($"编辑实验：{experiment.Name}");
    }

    /// <summary>
    /// 删除实验
    /// </summary>
    private async void DeleteExperiment(Experiment? experiment)
    {
        if (experiment == null) return;

        // 这里应该显示确认对话框
        var result = MessageBox.Show($"确定要删除实验 '{experiment.Name}' 吗？", 
            "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);

        if (result == MessageBoxResult.Yes)
        {
            await ExecuteAsync(async () =>
            {
                await _experimentService.DeleteExperimentAsync(experiment.Id);
                _eventPublisher.PublishExperimentDeleted(experiment.Id, experiment.Name);
            }, "正在删除实验...");
        }
    }

    /// <summary>
    /// 开始实验
    /// </summary>
    private async void StartExperiment(Experiment? experiment)
    {
        if (experiment == null) return;

        await ExecuteAsync(async () =>
        {
            await _experimentService.StartExperimentAsync(experiment.Id);
        }, "正在开始实验...");
    }

    /// <summary>
    /// 暂停实验
    /// </summary>
    private async void PauseExperiment(Experiment? experiment)
    {
        if (experiment == null) return;

        await ExecuteAsync(async () =>
        {
            await _experimentService.PauseExperimentAsync(experiment.Id);
        }, "正在暂停实验...");
    }

    /// <summary>
    /// 清除筛选条件
    /// </summary>
    private void ClearFilters()
    {
        SearchText = string.Empty;
        SelectedType = null;
        SelectedStatus = null;
        ShowOnlyMyExperiments = false;
    }

    #endregion

    #region 命令可执行条件

    private bool CanViewExperiment(Experiment? experiment) => experiment != null;
    private bool CanEditExperiment(Experiment? experiment) => experiment?.Status == ExperimentStatus.Created;
    private bool CanDeleteExperiment(Experiment? experiment) => experiment?.Status != ExperimentStatus.Running;
    private bool CanStartExperiment(Experiment? experiment) => experiment?.Status == ExperimentStatus.Created;
    private bool CanPauseExperiment(Experiment? experiment) => experiment?.Status == ExperimentStatus.Running;

    #endregion

    #region 事件处理方法

    /// <summary>
    /// 处理实验创建事件
    /// </summary>
    private void OnExperimentCreated(ExperimentCreatedEventArgs args)
    {
        Experiments.Insert(0, args.Experiment);
        _ = ApplyFiltersAsync();
    }

    /// <summary>
    /// 处理实验状态变化事件
    /// </summary>
    private void OnExperimentStatusChanged(ExperimentStatusChangedEventArgs args)
    {
        var experiment = Experiments.FirstOrDefault(e => e.Id == args.ExperimentId);
        if (experiment != null)
        {
            experiment.Status = args.NewStatus;
            
            // 通知命令重新评估可执行状态
            ViewExperimentCommand.RaiseCanExecuteChanged();
            EditExperimentCommand.RaiseCanExecuteChanged();
            DeleteExperimentCommand.RaiseCanExecuteChanged();
            StartExperimentCommand.RaiseCanExecuteChanged();
            PauseExperimentCommand.RaiseCanExecuteChanged();
        }
    }

    /// <summary>
    /// 处理实验删除事件
    /// </summary>
    private void OnExperimentDeleted(ExperimentDeletedEventArgs args)
    {
        var experiment = Experiments.FirstOrDefault(e => e.Id == args.ExperimentId);
        if (experiment != null)
        {
            Experiments.Remove(experiment);
            FilteredExperiments.Remove(experiment);
        }
    }

    #endregion
}

/// <summary>
/// 参数替换访问器 - 用于表达式树的参数替换
/// 
/// 这个类展示了表达式树的高级操作：
/// 1. 继承ExpressionVisitor
/// 2. 重写Visit方法
/// 3. 实现参数替换逻辑
/// </summary>
public class ParameterReplacer : ExpressionVisitor
{
    private readonly ParameterExpression _oldParameter;
    private readonly ParameterExpression _newParameter;

    public ParameterReplacer(ParameterExpression oldParameter, ParameterExpression newParameter)
    {
        _oldParameter = oldParameter;
        _newParameter = newParameter;
    }

    protected override Expression VisitParameter(ParameterExpression node)
    {
        return node == _oldParameter ? _newParameter : base.VisitParameter(node);
    }
}
